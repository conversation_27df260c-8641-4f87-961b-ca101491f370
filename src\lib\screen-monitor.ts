// Enhanced Screen Monitoring System for Flora AI
// Implements real-time screen capture, OCR, and meeting platform detection

import { desktopCapturer } from 'electron';

export interface ScreenContextService {
  startMonitoring(): Promise<void>;
  stopMonitoring(): void;
  captureActiveWindow(): Promise<ScreenCapture>;
  analyzeContent(image: ImageData): Promise<ContentAnalysis>;
  detectMeetingPlatform(): Promise<MeetingPlatform>;
  extractTextFromScreen(): Promise<string>;
  isMonitoring(): boolean;
}

export interface ScreenCapture {
  id: string;
  timestamp: number;
  imageData: ImageData;
  windowTitle: string;
  applicationName: string;
  bounds: WindowBounds;
  isScreenSharing: boolean;
}

export interface WindowBounds {
  x: number;
  y: number;
  width: number;
  height: number;
}

export interface ContentAnalysis {
  text: string;
  elements: UIElement[];
  meetingPlatform: MeetingPlatform | null;
  participants: Participant[];
  screenContent: ScreenContent;
  confidence: number;
}

export interface UIElement {
  type: 'button' | 'input' | 'text' | 'image' | 'video' | 'menu';
  bounds: WindowBounds;
  text?: string;
  confidence: number;
}

export interface MeetingPlatform {
  name: 'zoom' | 'teams' | 'meet' | 'webex' | 'discord' | 'slack' | 'unknown';
  version?: string;
  isRecording: boolean;
  isScreenSharing: boolean;
  participantCount: number;
  meetingId?: string;
}

export interface Participant {
  id: string;
  name: string;
  isSpeaking: boolean;
  isMuted: boolean;
  isVideoOn: boolean;
  role: 'host' | 'participant' | 'presenter';
}

export interface ScreenContent {
  type: 'meeting' | 'presentation' | 'code' | 'document' | 'browser' | 'desktop';
  title: string;
  context: string;
  keywords: string[];
  importance: 'low' | 'medium' | 'high' | 'critical';
}

class AdvancedScreenMonitor implements ScreenContextService {
  private isActive = false;
  private captureInterval: NodeJS.Timeout | null = null;
  private currentCapture: ScreenCapture | null = null;
  private ocrWorker: any = null;
  private platformDetectors: Map<string, RegExp[]> = new Map();

  constructor() {
    this.initializePlatformDetectors();
    this.initializeOCR();
  }

  private initializePlatformDetectors(): void {
    // Platform detection patterns based on window titles and UI elements
    this.platformDetectors.set('zoom', [
      /zoom\s*meeting/i,
      /zoom\s*webinar/i,
      /personal\s*meeting\s*room/i,
      /waiting\s*room/i
    ]);

    this.platformDetectors.set('teams', [
      /microsoft\s*teams/i,
      /teams\s*meeting/i,
      /teams\s*call/i,
      /join\s*microsoft\s*teams/i
    ]);

    this.platformDetectors.set('meet', [
      /google\s*meet/i,
      /meet\.google\.com/i,
      /hangouts\s*meet/i
    ]);

    this.platformDetectors.set('webex', [
      /cisco\s*webex/i,
      /webex\s*meetings/i,
      /webex\s*teams/i
    ]);

    this.platformDetectors.set('discord', [
      /discord/i,
      /voice\s*channel/i,
      /stage\s*channel/i
    ]);

    this.platformDetectors.set('slack', [
      /slack\s*call/i,
      /slack\s*huddle/i,
      /slack\s*meeting/i
    ]);
  }

  private async initializeOCR(): Promise<void> {
    try {
      // Initialize Tesseract.js for OCR functionality
      // In a real implementation, you'd import and configure Tesseract
      console.log('OCR initialized');
    } catch (error) {
      console.error('Failed to initialize OCR:', error);
    }
  }

  async startMonitoring(): Promise<void> {
    if (this.isActive) {
      console.log('Screen monitoring already active');
      return;
    }

    try {
      this.isActive = true;
      
      // Start continuous screen capture
      this.captureInterval = setInterval(async () => {
        try {
          await this.captureAndAnalyze();
        } catch (error) {
          console.error('Screen capture error:', error);
        }
      }, 1000); // Capture every second

      console.log('Screen monitoring started successfully');
    } catch (error) {
      console.error('Failed to start screen monitoring:', error);
      throw new Error('Screen monitoring initialization failed');
    }
  }

  stopMonitoring(): void {
    this.isActive = false;

    if (this.captureInterval) {
      clearInterval(this.captureInterval);
      this.captureInterval = null;
    }

    console.log('Screen monitoring stopped');
  }

  private async captureAndAnalyze(): Promise<void> {
    if (!this.isActive) return;

    try {
      const capture = await this.captureActiveWindow();
      this.currentCapture = capture;

      // Analyze the captured content
      const analysis = await this.analyzeContent(capture.imageData);
      
      // Emit events for detected changes
      this.emitScreenAnalysis(capture, analysis);
    } catch (error) {
      console.error('Screen capture and analysis failed:', error);
    }
  }

  async captureActiveWindow(): Promise<ScreenCapture> {
    return new Promise(async (resolve, reject) => {
      try {
        const sources = await desktopCapturer.getSources({
          types: ['window', 'screen'],
          thumbnailSize: { width: 1920, height: 1080 }
        });

        if (sources.length === 0) {
          reject(new Error('No screen sources available'));
          return;
        }

        // Get the primary screen or active window
        const primarySource = sources.find(source => 
          source.name.includes('Entire Screen') || 
          source.name.includes('Screen 1')
        ) || sources[0];

        const canvas = document.createElement('canvas');
        const ctx = canvas.getContext('2d');
        
        if (!ctx) {
          reject(new Error('Failed to create canvas context'));
          return;
        }

        const img = new Image();
        img.onload = () => {
          canvas.width = img.width;
          canvas.height = img.height;
          ctx.drawImage(img, 0, 0);
          
          const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
          
          const capture: ScreenCapture = {
            id: `capture_${Date.now()}`,
            timestamp: Date.now(),
            imageData,
            windowTitle: primarySource.name,
            applicationName: this.extractApplicationName(primarySource.name),
            bounds: {
              x: 0,
              y: 0,
              width: canvas.width,
              height: canvas.height
            },
            isScreenSharing: this.detectScreenSharing(primarySource.name)
          };

          resolve(capture);
        };

        img.onerror = () => {
          reject(new Error('Failed to load screen capture'));
        };

        img.src = primarySource.thumbnail.toDataURL();
      } catch (error) {
        reject(error);
      }
    });
  }

  private extractApplicationName(windowTitle: string): string {
    // Extract application name from window title
    const patterns = [
      /^([^-]+)\s*-/,  // "App Name - Document"
      /^([^|]+)\s*\|/, // "App Name | Page"
      /^([^:]+)\s*:/,  // "App Name: Content"
    ];

    for (const pattern of patterns) {
      const match = windowTitle.match(pattern);
      if (match) {
        return match[1].trim();
      }
    }

    return windowTitle.split(' ')[0] || 'Unknown';
  }

  private detectScreenSharing(windowTitle: string): boolean {
    const screenSharingIndicators = [
      /screen\s*share/i,
      /sharing\s*screen/i,
      /present/i,
      /presentation/i,
      /screenshare/i
    ];

    return screenSharingIndicators.some(pattern => pattern.test(windowTitle));
  }

  async analyzeContent(image: ImageData): Promise<ContentAnalysis> {
    try {
      // Extract text using OCR
      const text = await this.performOCR(image);
      
      // Detect UI elements
      const elements = await this.detectUIElements(image);
      
      // Detect meeting platform
      const meetingPlatform = await this.detectMeetingPlatformFromContent(text, elements);
      
      // Extract participants if in a meeting
      const participants = await this.extractParticipants(text, elements, meetingPlatform);
      
      // Analyze screen content type and context
      const screenContent = this.analyzeScreenContent(text, elements);
      
      // Calculate overall confidence
      const confidence = this.calculateAnalysisConfidence(text, elements, meetingPlatform);

      return {
        text,
        elements,
        meetingPlatform,
        participants,
        screenContent,
        confidence
      };
    } catch (error) {
      console.error('Content analysis failed:', error);
      return {
        text: '',
        elements: [],
        meetingPlatform: null,
        participants: [],
        screenContent: {
          type: 'desktop',
          title: 'Unknown',
          context: '',
          keywords: [],
          importance: 'low'
        },
        confidence: 0
      };
    }
  }

  private async performOCR(image: ImageData): Promise<string> {
    try {
      // Convert ImageData to format suitable for OCR
      const canvas = document.createElement('canvas');
      const ctx = canvas.getContext('2d');
      
      if (!ctx) {
        throw new Error('Failed to create canvas context for OCR');
      }

      canvas.width = image.width;
      canvas.height = image.height;
      ctx.putImageData(image, 0, 0);

      // In a real implementation, you would use Tesseract.js here
      // const { data: { text } } = await this.ocrWorker.recognize(canvas);
      
      // For now, return a placeholder
      return 'OCR text extraction would appear here';
    } catch (error) {
      console.error('OCR processing failed:', error);
      return '';
    }
  }

  private async detectUIElements(image: ImageData): Promise<UIElement[]> {
    // Simplified UI element detection
    // In a real implementation, you'd use computer vision algorithms
    const elements: UIElement[] = [];
    
    // Mock detection of common UI elements
    elements.push({
      type: 'button',
      bounds: { x: 100, y: 100, width: 80, height: 30 },
      text: 'Join Meeting',
      confidence: 0.9
    });

    return elements;
  }

  private async detectMeetingPlatformFromContent(text: string, elements: UIElement[]): Promise<MeetingPlatform | null> {
    const lowerText = text.toLowerCase();
    
    for (const [platform, patterns] of this.platformDetectors) {
      for (const pattern of patterns) {
        if (pattern.test(lowerText)) {
          return {
            name: platform as MeetingPlatform['name'],
            isRecording: this.detectRecording(text, elements),
            isScreenSharing: this.detectScreenSharingInMeeting(text, elements),
            participantCount: this.countParticipants(text, elements),
            meetingId: this.extractMeetingId(text)
          };
        }
      }
    }

    return null;
  }

  private detectRecording(text: string, elements: UIElement[]): boolean {
    const recordingIndicators = [
      /recording/i,
      /rec\s*button/i,
      /being\s*recorded/i,
      /record\s*meeting/i
    ];

    return recordingIndicators.some(pattern => pattern.test(text)) ||
           elements.some(el => el.text && recordingIndicators.some(pattern => pattern.test(el.text)));
  }

  private detectScreenSharingInMeeting(text: string, elements: UIElement[]): boolean {
    const sharingIndicators = [
      /sharing\s*screen/i,
      /screen\s*share/i,
      /stop\s*sharing/i,
      /share\s*screen/i
    ];

    return sharingIndicators.some(pattern => pattern.test(text));
  }

  private countParticipants(text: string, elements: UIElement[]): number {
    // Extract participant count from meeting interface
    const participantMatch = text.match(/(\d+)\s*participants?/i);
    if (participantMatch) {
      return parseInt(participantMatch[1], 10);
    }

    // Count visible participant elements
    const participantElements = elements.filter(el => 
      el.type === 'video' || (el.text && /participant|attendee/i.test(el.text))
    );

    return Math.max(participantElements.length, 1);
  }

  private extractMeetingId(text: string): string | undefined {
    // Extract meeting ID patterns
    const patterns = [
      /meeting\s*id[:\s]*(\d{3}[\s-]?\d{3}[\s-]?\d{4})/i,
      /id[:\s]*(\d{9,11})/i,
      /room[:\s]*(\d+)/i
    ];

    for (const pattern of patterns) {
      const match = text.match(pattern);
      if (match) {
        return match[1].replace(/[\s-]/g, '');
      }
    }

    return undefined;
  }

  private async extractParticipants(text: string, elements: UIElement[], platform: MeetingPlatform | null): Promise<Participant[]> {
    const participants: Participant[] = [];
    
    // Extract participant names from text
    const namePatterns = [
      /([A-Z][a-z]+\s+[A-Z][a-z]+)/g, // First Last
      /@([a-zA-Z0-9._-]+)/g // @username
    ];

    for (const pattern of namePatterns) {
      let match;
      while ((match = pattern.exec(text)) !== null) {
        participants.push({
          id: `participant_${participants.length}`,
          name: match[1] || match[0],
          isSpeaking: false,
          isMuted: false,
          isVideoOn: true,
          role: 'participant'
        });
      }
    }

    return participants;
  }

  private analyzeScreenContent(text: string, elements: UIElement[]): ScreenContent {
    const lowerText = text.toLowerCase();
    
    // Determine content type
    let type: ScreenContent['type'] = 'desktop';
    let importance: ScreenContent['importance'] = 'low';
    
    if (/meeting|call|conference|zoom|teams|meet/i.test(lowerText)) {
      type = 'meeting';
      importance = 'high';
    } else if (/presentation|slides|powerpoint|keynote/i.test(lowerText)) {
      type = 'presentation';
      importance = 'medium';
    } else if (/code|programming|github|vscode|editor/i.test(lowerText)) {
      type = 'code';
      importance = 'medium';
    } else if (/document|word|pdf|text/i.test(lowerText)) {
      type = 'document';
      importance = 'low';
    } else if (/browser|chrome|firefox|safari|edge/i.test(lowerText)) {
      type = 'browser';
      importance = 'medium';
    }

    // Extract keywords
    const keywords = this.extractKeywords(text);
    
    return {
      type,
      title: this.extractTitle(text, elements),
      context: text.substring(0, 200), // First 200 characters as context
      keywords,
      importance
    };
  }

  private extractKeywords(text: string): string[] {
    // Simple keyword extraction
    const words = text.toLowerCase().split(/\s+/);
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
    
    const keywords = words
      .filter(word => word.length > 3 && !stopWords.has(word))
      .reduce((acc, word) => {
        acc[word] = (acc[word] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    return Object.entries(keywords)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  private extractTitle(text: string, elements: UIElement[]): string {
    // Try to find title from UI elements first
    const titleElement = elements.find(el => 
      el.type === 'text' && el.text && el.text.length > 5 && el.text.length < 100
    );

    if (titleElement?.text) {
      return titleElement.text;
    }

    // Extract title from text
    const lines = text.split('\n').filter(line => line.trim().length > 0);
    if (lines.length > 0) {
      return lines[0].substring(0, 50);
    }

    return 'Untitled';
  }

  private calculateAnalysisConfidence(text: string, elements: UIElement[], platform: MeetingPlatform | null): number {
    let confidence = 0.5; // Base confidence

    // Boost confidence based on text quality
    if (text.length > 100) confidence += 0.2;
    if (text.length > 500) confidence += 0.1;

    // Boost confidence based on UI elements detected
    confidence += Math.min(elements.length * 0.05, 0.2);

    // Boost confidence if meeting platform detected
    if (platform) confidence += 0.2;

    return Math.min(Math.max(confidence, 0), 1);
  }

  async detectMeetingPlatform(): Promise<MeetingPlatform> {
    if (this.currentCapture) {
      const analysis = await this.analyzeContent(this.currentCapture.imageData);
      return analysis.meetingPlatform || {
        name: 'unknown',
        isRecording: false,
        isScreenSharing: false,
        participantCount: 0
      };
    }

    return {
      name: 'unknown',
      isRecording: false,
      isScreenSharing: false,
      participantCount: 0
    };
  }

  async extractTextFromScreen(): Promise<string> {
    if (this.currentCapture) {
      const analysis = await this.analyzeContent(this.currentCapture.imageData);
      return analysis.text;
    }
    return '';
  }

  private emitScreenAnalysis(capture: ScreenCapture, analysis: ContentAnalysis): void {
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('screenAnalysis', {
        detail: { capture, analysis }
      });
      window.dispatchEvent(event);
    }
  }

  isMonitoring(): boolean {
    return this.isActive;
  }
}

// Export singleton instance
export const screenMonitor = new AdvancedScreenMonitor();
