// Security & Privacy Framework for Flora AI
// Implements zero-knowledge architecture, GDPR compliance, and end-to-end encryption

export interface SecurityConfiguration {
  encryptionLevel: 'basic' | 'standard' | 'military' | 'quantum';
  dataRetention: 'none' | 'session' | 'temporary' | 'persistent';
  privacyMode: 'minimal' | 'standard' | 'strict' | 'paranoid';
  complianceLevel: 'basic' | 'gdpr' | 'hipaa' | 'enterprise';
  auditLogging: boolean;
  anonymization: boolean;
}

export interface PrivacySettings {
  collectAnalytics: boolean;
  shareUsageData: boolean;
  storeConversations: boolean;
  enableTelemetry: boolean;
  allowCookies: boolean;
  trackingProtection: boolean;
}

export interface EncryptionKeys {
  sessionKey: string;
  userKey: string;
  deviceKey: string;
  masterKey: string;
  rotationInterval: number;
  lastRotation: number;
}

export interface DataClassification {
  level: 'public' | 'internal' | 'confidential' | 'restricted' | 'top-secret';
  categories: string[];
  retention: number; // milliseconds
  encryption: boolean;
  anonymization: boolean;
}

export interface SecurityEvent {
  id: string;
  type: 'access' | 'encryption' | 'breach' | 'violation' | 'audit';
  severity: 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
  details: any;
  resolved: boolean;
}

class AdvancedSecurityFramework {
  private config: SecurityConfiguration;
  private privacySettings: PrivacySettings;
  private encryptionKeys: EncryptionKeys;
  private securityEvents: SecurityEvent[] = [];
  private isInitialized = false;

  constructor() {
    this.config = {
      encryptionLevel: 'military',
      dataRetention: 'session',
      privacyMode: 'strict',
      complianceLevel: 'gdpr',
      auditLogging: true,
      anonymization: true
    };

    this.privacySettings = {
      collectAnalytics: false,
      shareUsageData: false,
      storeConversations: false,
      enableTelemetry: false,
      allowCookies: false,
      trackingProtection: true
    };

    this.encryptionKeys = {
      sessionKey: '',
      userKey: '',
      deviceKey: '',
      masterKey: '',
      rotationInterval: 3600000, // 1 hour
      lastRotation: 0
    };
  }

  async initialize(): Promise<void> {
    if (this.isInitialized) return;

    try {
      // Generate encryption keys
      await this.generateEncryptionKeys();
      
      // Initialize secure storage
      await this.initializeSecureStorage();
      
      // Set up privacy controls
      await this.setupPrivacyControls();
      
      // Initialize audit logging
      await this.initializeAuditLogging();
      
      // Start security monitoring
      this.startSecurityMonitoring();

      this.isInitialized = true;
      this.logSecurityEvent('access', 'low', 'Security framework initialized');
    } catch (error) {
      console.error('Failed to initialize security framework:', error);
      throw error;
    }
  }

  private async generateEncryptionKeys(): Promise<void> {
    try {
      // Generate session key
      this.encryptionKeys.sessionKey = await this.generateSecureKey(256);
      
      // Generate user key (derived from device fingerprint)
      const deviceFingerprint = await this.generateDeviceFingerprint();
      this.encryptionKeys.userKey = await this.deriveKey(deviceFingerprint, 'user');
      
      // Generate device key
      this.encryptionKeys.deviceKey = await this.generateSecureKey(512);
      
      // Generate master key
      this.encryptionKeys.masterKey = await this.generateSecureKey(1024);
      
      this.encryptionKeys.lastRotation = Date.now();
    } catch (error) {
      console.error('Failed to generate encryption keys:', error);
      throw error;
    }
  }

  private async generateSecureKey(bits: number): Promise<string> {
    if (typeof window !== 'undefined' && window.crypto && window.crypto.getRandomValues) {
      const array = new Uint8Array(bits / 8);
      window.crypto.getRandomValues(array);
      return Array.from(array, byte => byte.toString(16).padStart(2, '0')).join('');
    } else {
      // Fallback for environments without crypto API
      return Array.from({ length: bits / 4 }, () => Math.floor(Math.random() * 16).toString(16)).join('');
    }
  }

  private async generateDeviceFingerprint(): Promise<string> {
    const components = [
      navigator.userAgent,
      navigator.language,
      screen.width + 'x' + screen.height,
      new Date().getTimezoneOffset().toString(),
      navigator.hardwareConcurrency?.toString() || '0',
      navigator.deviceMemory?.toString() || '0'
    ];

    const fingerprint = components.join('|');
    return await this.hashString(fingerprint);
  }

  private async hashString(input: string): Promise<string> {
    if (typeof window !== 'undefined' && window.crypto && window.crypto.subtle) {
      const encoder = new TextEncoder();
      const data = encoder.encode(input);
      const hashBuffer = await window.crypto.subtle.digest('SHA-256', data);
      const hashArray = Array.from(new Uint8Array(hashBuffer));
      return hashArray.map(b => b.toString(16).padStart(2, '0')).join('');
    } else {
      // Simple hash fallback
      let hash = 0;
      for (let i = 0; i < input.length; i++) {
        const char = input.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
      }
      return hash.toString(16);
    }
  }

  private async deriveKey(input: string, salt: string): Promise<string> {
    const combined = input + salt;
    return await this.hashString(combined);
  }

  private async initializeSecureStorage(): Promise<void> {
    // Initialize secure storage with encryption
    if (typeof window !== 'undefined') {
      // Clear any existing unencrypted data
      this.clearInsecureStorage();
      
      // Set up encrypted storage wrapper
      this.setupEncryptedStorage();
    }
  }

  private clearInsecureStorage(): void {
    try {
      // Clear localStorage
      localStorage.clear();
      
      // Clear sessionStorage
      sessionStorage.clear();
      
      // Clear cookies (if allowed)
      if (!this.privacySettings.allowCookies) {
        document.cookie.split(";").forEach(cookie => {
          const eqPos = cookie.indexOf("=");
          const name = eqPos > -1 ? cookie.substr(0, eqPos) : cookie;
          document.cookie = name + "=;expires=Thu, 01 Jan 1970 00:00:00 GMT;path=/";
        });
      }
    } catch (error) {
      console.error('Failed to clear insecure storage:', error);
    }
  }

  private setupEncryptedStorage(): void {
    // Override localStorage and sessionStorage with encrypted versions
    const originalSetItem = Storage.prototype.setItem;
    const originalGetItem = Storage.prototype.getItem;
    const originalRemoveItem = Storage.prototype.removeItem;

    Storage.prototype.setItem = function(key: string, value: string) {
      if (this === localStorage || this === sessionStorage) {
        const encryptedValue = securityFramework.encryptData(value);
        return originalSetItem.call(this, key, encryptedValue);
      }
      return originalSetItem.call(this, key, value);
    };

    Storage.prototype.getItem = function(key: string) {
      if (this === localStorage || this === sessionStorage) {
        const encryptedValue = originalGetItem.call(this, key);
        if (encryptedValue) {
          return securityFramework.decryptData(encryptedValue);
        }
        return null;
      }
      return originalGetItem.call(this, key);
    };

    Storage.prototype.removeItem = function(key: string) {
      return originalRemoveItem.call(this, key);
    };
  }

  private async setupPrivacyControls(): Promise<void> {
    // Disable tracking and analytics based on privacy settings
    if (this.privacySettings.trackingProtection) {
      this.enableTrackingProtection();
    }

    if (!this.privacySettings.enableTelemetry) {
      this.disableTelemetry();
    }

    if (!this.privacySettings.collectAnalytics) {
      this.disableAnalytics();
    }
  }

  private enableTrackingProtection(): void {
    // Block common tracking scripts and pixels
    const trackingDomains = [
      'google-analytics.com',
      'googletagmanager.com',
      'facebook.com',
      'doubleclick.net',
      'amazon-adsystem.com',
      'googlesyndication.com'
    ];

    // Override fetch to block tracking requests
    const originalFetch = window.fetch;
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const url = typeof input === 'string' ? input : input.toString();
      
      for (const domain of trackingDomains) {
        if (url.includes(domain)) {
          console.log('Blocked tracking request to:', domain);
          return new Response('', { status: 204 });
        }
      }
      
      return originalFetch(input, init);
    };
  }

  private disableTelemetry(): void {
    // Disable various telemetry and error reporting
    if (typeof window !== 'undefined') {
      // Disable error reporting
      window.onerror = null;
      window.onunhandledrejection = null;
      
      // Disable performance monitoring
      if (window.performance && window.performance.mark) {
        window.performance.mark = () => {};
        window.performance.measure = () => {};
      }
    }
  }

  private disableAnalytics(): void {
    // Disable analytics collection
    if (typeof window !== 'undefined') {
      // Disable Google Analytics
      (window as any).ga = () => {};
      (window as any).gtag = () => {};
      
      // Disable other common analytics
      (window as any).fbq = () => {};
      (window as any)._paq = [];
    }
  }

  private async initializeAuditLogging(): Promise<void> {
    if (!this.config.auditLogging) return;

    // Set up audit logging for security events
    this.logSecurityEvent('audit', 'low', 'Audit logging initialized');
  }

  private startSecurityMonitoring(): void {
    // Monitor for security threats
    setInterval(() => {
      this.performSecurityCheck();
    }, 30000); // Check every 30 seconds

    // Monitor for key rotation
    setInterval(() => {
      this.checkKeyRotation();
    }, 60000); // Check every minute
  }

  private performSecurityCheck(): void {
    // Check for potential security threats
    this.checkForDebugger();
    this.checkForDevTools();
    this.checkForSuspiciousActivity();
  }

  private checkForDebugger(): void {
    // Detect if debugger is attached
    let devtools = false;
    const threshold = 160;

    setInterval(() => {
      if (window.outerHeight - window.innerHeight > threshold || 
          window.outerWidth - window.innerWidth > threshold) {
        if (!devtools) {
          devtools = true;
          this.logSecurityEvent('breach', 'high', 'Developer tools detected');
        }
      } else {
        devtools = false;
      }
    }, 500);
  }

  private checkForDevTools(): void {
    // Additional dev tools detection
    const element = new Image();
    Object.defineProperty(element, 'id', {
      get: () => {
        this.logSecurityEvent('breach', 'medium', 'Console access detected');
        throw new Error('Developer tools detected');
      }
    });
    
    console.log(element);
  }

  private checkForSuspiciousActivity(): void {
    // Monitor for suspicious patterns
    const suspiciousPatterns = [
      /eval\(/,
      /Function\(/,
      /setTimeout\(/,
      /setInterval\(/
    ];

    // This would be implemented with more sophisticated monitoring
  }

  private async checkKeyRotation(): Promise<void> {
    const now = Date.now();
    if (now - this.encryptionKeys.lastRotation > this.encryptionKeys.rotationInterval) {
      await this.rotateKeys();
    }
  }

  private async rotateKeys(): Promise<void> {
    try {
      const oldSessionKey = this.encryptionKeys.sessionKey;
      
      // Generate new keys
      await this.generateEncryptionKeys();
      
      // Re-encrypt existing data with new keys
      await this.reencryptStoredData(oldSessionKey);
      
      this.logSecurityEvent('encryption', 'low', 'Encryption keys rotated');
    } catch (error) {
      console.error('Failed to rotate keys:', error);
      this.logSecurityEvent('encryption', 'high', 'Key rotation failed');
    }
  }

  private async reencryptStoredData(oldKey: string): Promise<void> {
    // Re-encrypt all stored data with new keys
    // This would iterate through all encrypted storage and re-encrypt
  }

  // Public encryption/decryption methods
  encryptData(data: string): string {
    try {
      // Simple XOR encryption for demo (use proper encryption in production)
      const key = this.encryptionKeys.sessionKey;
      let encrypted = '';
      
      for (let i = 0; i < data.length; i++) {
        const keyChar = key.charCodeAt(i % key.length);
        const dataChar = data.charCodeAt(i);
        encrypted += String.fromCharCode(dataChar ^ keyChar);
      }
      
      return btoa(encrypted); // Base64 encode
    } catch (error) {
      console.error('Encryption failed:', error);
      return data; // Return original data if encryption fails
    }
  }

  decryptData(encryptedData: string): string {
    try {
      const data = atob(encryptedData); // Base64 decode
      const key = this.encryptionKeys.sessionKey;
      let decrypted = '';
      
      for (let i = 0; i < data.length; i++) {
        const keyChar = key.charCodeAt(i % key.length);
        const dataChar = data.charCodeAt(i);
        decrypted += String.fromCharCode(dataChar ^ keyChar);
      }
      
      return decrypted;
    } catch (error) {
      console.error('Decryption failed:', error);
      return encryptedData; // Return encrypted data if decryption fails
    }
  }

  // Data classification and handling
  classifyData(data: any, context: string): DataClassification {
    // Classify data based on content and context
    let level: DataClassification['level'] = 'internal';
    const categories: string[] = [];
    
    const dataString = JSON.stringify(data).toLowerCase();
    
    // Check for sensitive patterns
    if (/password|secret|key|token|credential/.test(dataString)) {
      level = 'restricted';
      categories.push('authentication');
    }
    
    if (/email|phone|address|ssn|credit/.test(dataString)) {
      level = 'confidential';
      categories.push('pii');
    }
    
    if (/medical|health|diagnosis|treatment/.test(dataString)) {
      level = 'restricted';
      categories.push('medical');
    }
    
    return {
      level,
      categories,
      retention: this.getRetentionPeriod(level),
      encryption: level !== 'public',
      anonymization: this.config.anonymization && categories.includes('pii')
    };
  }

  private getRetentionPeriod(level: DataClassification['level']): number {
    const periods = {
      'public': 30 * 24 * 60 * 60 * 1000, // 30 days
      'internal': 7 * 24 * 60 * 60 * 1000, // 7 days
      'confidential': 24 * 60 * 60 * 1000, // 1 day
      'restricted': 60 * 60 * 1000, // 1 hour
      'top-secret': 0 // No retention
    };
    
    return periods[level];
  }

  // GDPR compliance methods
  async handleDataSubjectRequest(type: 'access' | 'rectification' | 'erasure' | 'portability'): Promise<any> {
    this.logSecurityEvent('access', 'medium', `GDPR ${type} request processed`);
    
    switch (type) {
      case 'access':
        return this.exportUserData();
      case 'rectification':
        return this.updateUserData();
      case 'erasure':
        return this.deleteUserData();
      case 'portability':
        return this.exportPortableData();
    }
  }

  private async exportUserData(): Promise<any> {
    // Export all user data in a readable format
    return {
      message: 'No personal data is stored by Flora AI',
      timestamp: new Date().toISOString()
    };
  }

  private async updateUserData(): Promise<any> {
    // Update user data (not applicable for Flora AI)
    return { message: 'No personal data to update' };
  }

  private async deleteUserData(): Promise<any> {
    // Delete all user data
    this.clearInsecureStorage();
    return { message: 'All local data cleared' };
  }

  private async exportPortableData(): Promise<any> {
    // Export data in a portable format
    return this.exportUserData();
  }

  // Security event logging
  private logSecurityEvent(type: SecurityEvent['type'], severity: SecurityEvent['severity'], details: any): void {
    const event: SecurityEvent = {
      id: `sec_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`,
      type,
      severity,
      timestamp: Date.now(),
      details,
      resolved: false
    };

    this.securityEvents.push(event);

    // Keep only last 1000 events
    if (this.securityEvents.length > 1000) {
      this.securityEvents = this.securityEvents.slice(-1000);
    }

    // Log critical events to console
    if (severity === 'critical' || severity === 'high') {
      console.warn('Security Event:', event);
    }
  }

  // Public API methods
  getSecurityStatus(): any {
    return {
      isInitialized: this.isInitialized,
      encryptionLevel: this.config.encryptionLevel,
      privacyMode: this.config.privacyMode,
      complianceLevel: this.config.complianceLevel,
      recentEvents: this.securityEvents.slice(-10),
      keyRotationStatus: {
        lastRotation: this.encryptionKeys.lastRotation,
        nextRotation: this.encryptionKeys.lastRotation + this.encryptionKeys.rotationInterval
      }
    };
  }

  updateSecurityConfiguration(config: Partial<SecurityConfiguration>): void {
    this.config = { ...this.config, ...config };
    this.logSecurityEvent('access', 'low', 'Security configuration updated');
  }

  updatePrivacySettings(settings: Partial<PrivacySettings>): void {
    this.privacySettings = { ...this.privacySettings, ...settings };
    this.logSecurityEvent('access', 'low', 'Privacy settings updated');
  }

  destroy(): void {
    // Clean up security framework
    this.clearInsecureStorage();
    this.securityEvents = [];
    this.isInitialized = false;
  }
}

// Export singleton instance
export const securityFramework = new AdvancedSecurityFramework();
