// Flora AI Integration Test Suite
// Comprehensive testing of all systems working together

import { floraAICore } from './flora-ai-core';
import { audioProcessor } from './audio-processor';
import { screenMonitor } from './screen-monitor';
import { aiOrchestrator } from './ai-orchestrator';
import { stealthManager } from './stealth-manager';
import { contextEngine } from './context-engine';
import { securityFramework } from './security-framework';
import { performanceOptimizer } from './performance-optimizer';

export interface TestResult {
  testName: string;
  passed: boolean;
  duration: number;
  details: string;
  timestamp: number;
}

export interface IntegrationTestReport {
  overallPassed: boolean;
  totalTests: number;
  passedTests: number;
  failedTests: number;
  totalDuration: number;
  results: TestResult[];
  systemStatus: any;
  timestamp: number;
}

class FloraAIIntegrationTester {
  private testResults: TestResult[] = [];

  async runFullIntegrationTest(): Promise<IntegrationTestReport> {
    console.log('🧪 Starting Flora AI Integration Test Suite...');
    const startTime = Date.now();

    // Reset test results
    this.testResults = [];

    try {
      // Core System Tests
      await this.testCoreInitialization();
      await this.testSystemCommunication();
      
      // Individual System Tests
      await this.testAudioProcessing();
      await this.testScreenMonitoring();
      await this.testAIOrchestration();
      await this.testStealthTechnology();
      await this.testContextEngine();
      await this.testSecurityFramework();
      await this.testPerformanceOptimization();
      
      // Integration Tests
      await this.testEndToEndWorkflow();
      await this.testStealthIntegration();
      await this.testSecurityIntegration();
      await this.testPerformanceIntegration();
      
      // Stress Tests
      await this.testConcurrentOperations();
      await this.testResourceLimits();
      await this.testErrorRecovery();

    } catch (error) {
      console.error('Integration test suite failed:', error);
      this.addTestResult('Integration Test Suite', false, 0, `Suite failed: ${error}`);
    }

    const totalDuration = Date.now() - startTime;
    const passedTests = this.testResults.filter(r => r.passed).length;
    const failedTests = this.testResults.filter(r => !r.passed).length;

    const report: IntegrationTestReport = {
      overallPassed: failedTests === 0,
      totalTests: this.testResults.length,
      passedTests,
      failedTests,
      totalDuration,
      results: this.testResults,
      systemStatus: floraAICore.getSystemStatus(),
      timestamp: Date.now()
    };

    console.log(`🧪 Integration Test Complete: ${passedTests}/${this.testResults.length} passed`);
    return report;
  }

  private async testCoreInitialization(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test core initialization
      await floraAICore.initialize({
        enableAudioProcessing: true,
        enableScreenMonitoring: true,
        enableStealthMode: true,
        enableContextAnalysis: true,
        enableSecurity: true,
        enablePerformanceOptimization: true,
        stealthLevel: 'maximum',
        securityLevel: 'military',
        performanceMode: 'balanced'
      });

      const status = floraAICore.getSystemStatus();
      const passed = status.isInitialized && status.overallHealth !== 'critical';
      
      this.addTestResult(
        'Core System Initialization',
        passed,
        Date.now() - testStart,
        passed ? 'All systems initialized successfully' : `Initialization failed: ${status.systemsOffline.join(', ')}`
      );
    } catch (error) {
      this.addTestResult(
        'Core System Initialization',
        false,
        Date.now() - testStart,
        `Initialization error: ${error}`
      );
    }
  }

  private async testSystemCommunication(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test inter-system communication
      let communicationWorking = true;
      let details = '';

      // Test event system
      let eventReceived = false;
      const testEventHandler = () => { eventReceived = true; };
      
      if (typeof window !== 'undefined') {
        window.addEventListener('floraAISystemEvent', testEventHandler);
        
        // Trigger a test event
        const event = new CustomEvent('floraAISystemEvent', {
          detail: { type: 'test', data: 'integration-test' }
        });
        window.dispatchEvent(event);
        
        // Wait for event processing
        await new Promise(resolve => setTimeout(resolve, 100));
        
        if (!eventReceived) {
          communicationWorking = false;
          details += 'Event system not working. ';
        }
        
        window.removeEventListener('floraAISystemEvent', testEventHandler);
      }

      this.addTestResult(
        'System Communication',
        communicationWorking,
        Date.now() - testStart,
        communicationWorking ? 'Inter-system communication working' : details
      );
    } catch (error) {
      this.addTestResult(
        'System Communication',
        false,
        Date.now() - testStart,
        `Communication test error: ${error}`
      );
    }
  }

  private async testAudioProcessing(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test audio processing capabilities
      const isMonitoring = audioProcessor.isMonitoring();
      
      // Test question detection
      const testQuestions = audioProcessor.detectQuestions(
        "What is your experience with React? How would you implement this feature?"
      );
      
      const passed = testQuestions.length >= 2;
      
      this.addTestResult(
        'Audio Processing',
        passed,
        Date.now() - testStart,
        passed ? `Detected ${testQuestions.length} questions correctly` : 'Question detection failed'
      );
    } catch (error) {
      this.addTestResult(
        'Audio Processing',
        false,
        Date.now() - testStart,
        `Audio processing error: ${error}`
      );
    }
  }

  private async testScreenMonitoring(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test screen monitoring capabilities
      const isMonitoring = screenMonitor.isMonitoring();
      const platform = await screenMonitor.detectMeetingPlatform();
      
      const passed = typeof platform === 'object';
      
      this.addTestResult(
        'Screen Monitoring',
        passed,
        Date.now() - testStart,
        passed ? `Screen monitoring active, platform: ${platform.name}` : 'Screen monitoring failed'
      );
    } catch (error) {
      this.addTestResult(
        'Screen Monitoring',
        false,
        Date.now() - testStart,
        `Screen monitoring error: ${error}`
      );
    }
  }

  private async testAIOrchestration(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test AI orchestration with a simple query
      const testContext = {
        type: 'interview' as const,
        phase: 'questions' as const,
        participants: 2,
        duration: 1800000,
        isRecording: false,
        isScreenSharing: false,
        platform: 'zoom',
        urgency: 'medium' as const,
        keywords: ['react', 'javascript', 'frontend']
      };

      const response = await aiOrchestrator.processIntelligently(
        "What is React and how does it work?",
        testContext
      );

      const passed = response && response.content && response.content.length > 50;
      
      this.addTestResult(
        'AI Orchestration',
        passed,
        Date.now() - testStart,
        passed ? `AI response generated (${response.content.length} chars, ${response.confidence} confidence)` : 'AI orchestration failed'
      );
    } catch (error) {
      this.addTestResult(
        'AI Orchestration',
        false,
        Date.now() - testStart,
        `AI orchestration error: ${error}`
      );
    }
  }

  private async testStealthTechnology(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test stealth capabilities
      const status = stealthManager.getStealthStatus();
      const activeLayers = status.protectionLayers.filter(layer => layer.status === 'active').length;
      
      // Test emergency hide/show
      await stealthManager.emergencyHide();
      await new Promise(resolve => setTimeout(resolve, 100));
      await stealthManager.emergencyShow();
      
      const passed = status.isActive && activeLayers >= 10;
      
      this.addTestResult(
        'Stealth Technology',
        passed,
        Date.now() - testStart,
        passed ? `${activeLayers} protection layers active, confidence: ${Math.round(status.confidence * 100)}%` : 'Stealth system not fully operational'
      );
    } catch (error) {
      this.addTestResult(
        'Stealth Technology',
        false,
        Date.now() - testStart,
        `Stealth technology error: ${error}`
      );
    }
  }

  private async testContextEngine(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test context engine capabilities
      const isMonitoring = contextEngine.isMonitoring();
      const currentContext = contextEngine.getCurrentContext();
      const meetingState = contextEngine.getMeetingState();
      
      const passed = isMonitoring && meetingState;
      
      this.addTestResult(
        'Context Engine',
        passed,
        Date.now() - testStart,
        passed ? `Context monitoring active, meeting type: ${meetingState.type}` : 'Context engine not operational'
      );
    } catch (error) {
      this.addTestResult(
        'Context Engine',
        false,
        Date.now() - testStart,
        `Context engine error: ${error}`
      );
    }
  }

  private async testSecurityFramework(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test security capabilities
      const securityStatus = securityFramework.getSecurityStatus();
      
      // Test encryption/decryption
      const testData = "This is sensitive test data";
      const encrypted = securityFramework.encryptData(testData);
      const decrypted = securityFramework.decryptData(encrypted);
      
      const passed = securityStatus.isInitialized && decrypted === testData;
      
      this.addTestResult(
        'Security Framework',
        passed,
        Date.now() - testStart,
        passed ? `Security active, encryption level: ${securityStatus.encryptionLevel}` : 'Security framework not operational'
      );
    } catch (error) {
      this.addTestResult(
        'Security Framework',
        false,
        Date.now() - testStart,
        `Security framework error: ${error}`
      );
    }
  }

  private async testPerformanceOptimization(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test performance optimization
      const currentMetrics = performanceOptimizer.getCurrentMetrics();
      const report = performanceOptimizer.getPerformanceReport();
      
      const passed = report && report.averageResponseTime < 5000;
      
      this.addTestResult(
        'Performance Optimization',
        passed,
        Date.now() - testStart,
        passed ? `Performance optimized, avg response: ${Math.round(report.averageResponseTime)}ms` : 'Performance optimization not working'
      );
    } catch (error) {
      this.addTestResult(
        'Performance Optimization',
        false,
        Date.now() - testStart,
        `Performance optimization error: ${error}`
      );
    }
  }

  private async testEndToEndWorkflow(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test complete end-to-end workflow
      const query = "How do I implement a React component with TypeScript?";
      const response = await floraAICore.processQuery(query);
      
      const passed = response && response.content && response.content.length > 100;
      
      this.addTestResult(
        'End-to-End Workflow',
        passed,
        Date.now() - testStart,
        passed ? `Complete workflow successful, response: ${response.content.substring(0, 100)}...` : 'End-to-end workflow failed'
      );
    } catch (error) {
      this.addTestResult(
        'End-to-End Workflow',
        false,
        Date.now() - testStart,
        `End-to-end workflow error: ${error}`
      );
    }
  }

  private async testStealthIntegration(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test stealth integration with other systems
      const stealthStatus = stealthManager.getStealthStatus();
      const contextState = contextEngine.getMeetingState();
      
      // Simulate threat detection
      const threatEvent = new CustomEvent('threatDetected', {
        detail: { 
          threat: { 
            type: 'screen-sharing', 
            severity: 'high', 
            timestamp: Date.now() 
          } 
        }
      });
      
      if (typeof window !== 'undefined') {
        window.dispatchEvent(threatEvent);
      }
      
      // Wait for response
      await new Promise(resolve => setTimeout(resolve, 200));
      
      const passed = stealthStatus.isActive;
      
      this.addTestResult(
        'Stealth Integration',
        passed,
        Date.now() - testStart,
        passed ? 'Stealth integration working with threat response' : 'Stealth integration failed'
      );
    } catch (error) {
      this.addTestResult(
        'Stealth Integration',
        false,
        Date.now() - testStart,
        `Stealth integration error: ${error}`
      );
    }
  }

  private async testSecurityIntegration(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test security integration across systems
      const securityStatus = securityFramework.getSecurityStatus();
      
      // Test data classification
      const testData = { email: '<EMAIL>', password: 'secret123' };
      const classification = securityFramework.classifyData(testData, 'user-input');
      
      const passed = classification.level === 'restricted' && classification.encryption;
      
      this.addTestResult(
        'Security Integration',
        passed,
        Date.now() - testStart,
        passed ? `Security integration working, data classified as: ${classification.level}` : 'Security integration failed'
      );
    } catch (error) {
      this.addTestResult(
        'Security Integration',
        false,
        Date.now() - testStart,
        `Security integration error: ${error}`
      );
    }
  }

  private async testPerformanceIntegration(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test performance integration
      const initialReport = performanceOptimizer.getPerformanceReport();
      
      // Simulate multiple concurrent requests
      const promises = Array.from({ length: 5 }, (_, i) => 
        floraAICore.processQuery(`Test query ${i + 1}`)
      );
      
      await Promise.allSettled(promises);
      
      const finalReport = performanceOptimizer.getPerformanceReport();
      const passed = finalReport.averageResponseTime < 10000; // 10 seconds max
      
      this.addTestResult(
        'Performance Integration',
        passed,
        Date.now() - testStart,
        passed ? `Performance integration working, avg response: ${Math.round(finalReport.averageResponseTime)}ms` : 'Performance integration failed'
      );
    } catch (error) {
      this.addTestResult(
        'Performance Integration',
        false,
        Date.now() - testStart,
        `Performance integration error: ${error}`
      );
    }
  }

  private async testConcurrentOperations(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test concurrent operations
      const operations = [
        floraAICore.processQuery("What is JavaScript?"),
        floraAICore.processQuery("How does React work?"),
        floraAICore.processQuery("Explain TypeScript benefits"),
      ];
      
      const results = await Promise.allSettled(operations);
      const successfulResults = results.filter(r => r.status === 'fulfilled').length;
      
      const passed = successfulResults >= 2; // At least 2 out of 3 should succeed
      
      this.addTestResult(
        'Concurrent Operations',
        passed,
        Date.now() - testStart,
        passed ? `${successfulResults}/3 concurrent operations successful` : 'Concurrent operations failed'
      );
    } catch (error) {
      this.addTestResult(
        'Concurrent Operations',
        false,
        Date.now() - testStart,
        `Concurrent operations error: ${error}`
      );
    }
  }

  private async testResourceLimits(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test resource limit handling
      const initialMetrics = performanceOptimizer.getCurrentMetrics();
      
      // Simulate resource pressure
      const largeQuery = "A".repeat(10000); // Large query
      const response = await floraAICore.processQuery(largeQuery);
      
      const finalMetrics = performanceOptimizer.getCurrentMetrics();
      const passed = response !== null; // Should handle gracefully
      
      this.addTestResult(
        'Resource Limits',
        passed,
        Date.now() - testStart,
        passed ? 'Resource limits handled gracefully' : 'Resource limit handling failed'
      );
    } catch (error) {
      this.addTestResult(
        'Resource Limits',
        false,
        Date.now() - testStart,
        `Resource limits error: ${error}`
      );
    }
  }

  private async testErrorRecovery(): Promise<void> {
    const testStart = Date.now();
    
    try {
      // Test error recovery mechanisms
      let recoveryWorking = true;
      
      // Test invalid query handling
      try {
        await floraAICore.processQuery("");
      } catch (error) {
        // Should handle empty queries gracefully
      }
      
      // Test system status after error
      const status = floraAICore.getSystemStatus();
      if (status.overallHealth === 'critical') {
        recoveryWorking = false;
      }
      
      this.addTestResult(
        'Error Recovery',
        recoveryWorking,
        Date.now() - testStart,
        recoveryWorking ? 'Error recovery mechanisms working' : 'Error recovery failed'
      );
    } catch (error) {
      this.addTestResult(
        'Error Recovery',
        false,
        Date.now() - testStart,
        `Error recovery test error: ${error}`
      );
    }
  }

  private addTestResult(testName: string, passed: boolean, duration: number, details: string): void {
    this.testResults.push({
      testName,
      passed,
      duration,
      details,
      timestamp: Date.now()
    });

    const status = passed ? '✅' : '❌';
    console.log(`${status} ${testName}: ${details} (${duration}ms)`);
  }

  // Quick health check for development
  async quickHealthCheck(): Promise<boolean> {
    try {
      const status = floraAICore.getSystemStatus();
      return status.isInitialized && status.overallHealth !== 'critical';
    } catch (error) {
      console.error('Quick health check failed:', error);
      return false;
    }
  }

  // Performance benchmark
  async runPerformanceBenchmark(): Promise<any> {
    const startTime = Date.now();
    const queries = [
      "What is React?",
      "How do I implement authentication?",
      "Explain database optimization",
      "What are design patterns?",
      "How does machine learning work?"
    ];

    const results = [];
    
    for (const query of queries) {
      const queryStart = Date.now();
      try {
        const response = await floraAICore.processQuery(query);
        results.push({
          query,
          duration: Date.now() - queryStart,
          success: true,
          responseLength: response.content.length
        });
      } catch (error) {
        results.push({
          query,
          duration: Date.now() - queryStart,
          success: false,
          error: error.toString()
        });
      }
    }

    return {
      totalDuration: Date.now() - startTime,
      averageDuration: results.reduce((sum, r) => sum + r.duration, 0) / results.length,
      successRate: results.filter(r => r.success).length / results.length,
      results
    };
  }
}

// Export singleton instance
export const integrationTester = new FloraAIIntegrationTester();

// Export for easy testing
export default integrationTester;
