// Flora AI Core Integration System
// Orchestrates all advanced systems for comprehensive AI assistance platform

import { audioProcessor } from './audio-processor';
import { screenMonitor } from './screen-monitor';
import { aiOrchestrator } from './ai-orchestrator';
import { stealthManager } from './stealth-manager';
import { contextEngine } from './context-engine';
import { securityFramework } from './security-framework';
import { performanceOptimizer } from './performance-optimizer';

export interface FloraAIConfig {
  enableAudioProcessing: boolean;
  enableScreenMonitoring: boolean;
  enableStealthMode: boolean;
  enableContextAnalysis: boolean;
  enableSecurity: boolean;
  enablePerformanceOptimization: boolean;
  stealthLevel: 'minimal' | 'standard' | 'maximum' | 'paranoid';
  securityLevel: 'basic' | 'standard' | 'military' | 'quantum';
  performanceMode: 'latency' | 'throughput' | 'balanced' | 'quality';
}

export interface FloraAIStatus {
  isInitialized: boolean;
  systemsOnline: string[];
  systemsOffline: string[];
  overallHealth: 'excellent' | 'good' | 'fair' | 'poor' | 'critical';
  lastUpdate: number;
  activeThreats: number;
  performanceScore: number;
  securityScore: number;
}

export interface FloraAICapabilities {
  audioProcessing: {
    speechToText: boolean;
    questionDetection: boolean;
    speakerIdentification: boolean;
    sentimentAnalysis: boolean;
  };
  screenAnalysis: {
    ocrCapability: boolean;
    meetingDetection: boolean;
    contentAnalysis: boolean;
    uiElementDetection: boolean;
  };
  aiProcessing: {
    multiModelSupport: boolean;
    contextAwareness: boolean;
    predictiveResponses: boolean;
    intelligentRouting: boolean;
  };
  stealth: {
    invisibilityLayers: number;
    threatDetection: boolean;
    dynamicPositioning: boolean;
    processObfuscation: boolean;
  };
  security: {
    encryptionLevel: string;
    privacyCompliance: boolean;
    auditLogging: boolean;
    dataProtection: boolean;
  };
  performance: {
    streamingResponses: boolean;
    adaptiveQuality: boolean;
    resourceOptimization: boolean;
    cacheOptimization: boolean;
  };
}

class FloraAICore {
  private config: FloraAIConfig;
  private isInitialized = false;
  private systemStatus: Map<string, boolean> = new Map();
  private healthCheckInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.config = {
      enableAudioProcessing: true,
      enableScreenMonitoring: true,
      enableStealthMode: true,
      enableContextAnalysis: true,
      enableSecurity: true,
      enablePerformanceOptimization: true,
      stealthLevel: 'maximum',
      securityLevel: 'military',
      performanceMode: 'balanced'
    };
  }

  async initialize(customConfig?: Partial<FloraAIConfig>): Promise<void> {
    if (this.isInitialized) {
      console.log('Flora AI Core already initialized');
      return;
    }

    // Apply custom configuration
    if (customConfig) {
      this.config = { ...this.config, ...customConfig };
    }

    console.log('🌸 Initializing Flora AI Core Systems...');

    try {
      // Initialize systems in order of dependency
      await this.initializeSecurityFramework();
      await this.initializePerformanceOptimizer();
      await this.initializeStealthManager();
      await this.initializeAudioProcessor();
      await this.initializeScreenMonitor();
      await this.initializeContextEngine();
      await this.initializeAIOrchestrator();

      // Start health monitoring
      this.startHealthMonitoring();

      this.isInitialized = true;
      console.log('✅ Flora AI Core fully initialized and operational');
      
      // Emit initialization complete event
      this.emitSystemEvent('initialized', { timestamp: Date.now() });

    } catch (error) {
      console.error('❌ Failed to initialize Flora AI Core:', error);
      throw new Error(`Flora AI initialization failed: ${error}`);
    }
  }

  private async initializeSecurityFramework(): Promise<void> {
    if (!this.config.enableSecurity) {
      this.systemStatus.set('security', false);
      return;
    }

    try {
      await securityFramework.initialize();
      securityFramework.updateSecurityConfiguration({
        encryptionLevel: this.config.securityLevel,
        privacyMode: 'strict',
        complianceLevel: 'gdpr',
        auditLogging: true,
        anonymization: true
      });
      
      this.systemStatus.set('security', true);
      console.log('🔒 Security Framework initialized');
    } catch (error) {
      console.error('Security Framework initialization failed:', error);
      this.systemStatus.set('security', false);
    }
  }

  private async initializePerformanceOptimizer(): Promise<void> {
    if (!this.config.enablePerformanceOptimization) {
      this.systemStatus.set('performance', false);
      return;
    }

    try {
      await performanceOptimizer.initialize();
      performanceOptimizer.updateSettings({
        enableStreaming: true,
        adaptiveQuality: true,
        resourceThrottling: true,
        cacheOptimization: true,
        priorityMode: this.config.performanceMode
      });
      
      this.systemStatus.set('performance', true);
      console.log('⚡ Performance Optimizer initialized');
    } catch (error) {
      console.error('Performance Optimizer initialization failed:', error);
      this.systemStatus.set('performance', false);
    }
  }

  private async initializeStealthManager(): Promise<void> {
    if (!this.config.enableStealthMode) {
      this.systemStatus.set('stealth', false);
      return;
    }

    try {
      await stealthManager.initialize();
      await stealthManager.setStealthLevel(this.config.stealthLevel);
      
      this.systemStatus.set('stealth', true);
      console.log('👻 Stealth Manager initialized');
    } catch (error) {
      console.error('Stealth Manager initialization failed:', error);
      this.systemStatus.set('stealth', false);
    }
  }

  private async initializeAudioProcessor(): Promise<void> {
    if (!this.config.enableAudioProcessing) {
      this.systemStatus.set('audio', false);
      return;
    }

    try {
      await audioProcessor.startMonitoring();
      this.systemStatus.set('audio', true);
      console.log('🎤 Audio Processor initialized');
    } catch (error) {
      console.error('Audio Processor initialization failed:', error);
      this.systemStatus.set('audio', false);
    }
  }

  private async initializeScreenMonitor(): Promise<void> {
    if (!this.config.enableScreenMonitoring) {
      this.systemStatus.set('screen', false);
      return;
    }

    try {
      await screenMonitor.startMonitoring();
      this.systemStatus.set('screen', true);
      console.log('🖥️ Screen Monitor initialized');
    } catch (error) {
      console.error('Screen Monitor initialization failed:', error);
      this.systemStatus.set('screen', false);
    }
  }

  private async initializeContextEngine(): Promise<void> {
    if (!this.config.enableContextAnalysis) {
      this.systemStatus.set('context', false);
      return;
    }

    try {
      await contextEngine.startContextMonitoring();
      this.systemStatus.set('context', true);
      console.log('🧠 Context Engine initialized');
    } catch (error) {
      console.error('Context Engine initialization failed:', error);
      this.systemStatus.set('context', false);
    }
  }

  private async initializeAIOrchestrator(): Promise<void> {
    try {
      // AI Orchestrator doesn't need explicit initialization
      this.systemStatus.set('ai', true);
      console.log('🤖 AI Orchestrator initialized');
    } catch (error) {
      console.error('AI Orchestrator initialization failed:', error);
      this.systemStatus.set('ai', false);
    }
  }

  private startHealthMonitoring(): void {
    this.healthCheckInterval = setInterval(() => {
      this.performHealthCheck();
    }, 10000); // Check every 10 seconds
  }

  private performHealthCheck(): void {
    // Check system health
    const systemsOnline = Array.from(this.systemStatus.entries())
      .filter(([, status]) => status)
      .map(([system]) => system);

    const systemsOffline = Array.from(this.systemStatus.entries())
      .filter(([, status]) => !status)
      .map(([system]) => system);

    // Calculate overall health
    const healthScore = systemsOnline.length / this.systemStatus.size;
    let overallHealth: FloraAIStatus['overallHealth'];

    if (healthScore >= 0.9) overallHealth = 'excellent';
    else if (healthScore >= 0.7) overallHealth = 'good';
    else if (healthScore >= 0.5) overallHealth = 'fair';
    else if (healthScore >= 0.3) overallHealth = 'poor';
    else overallHealth = 'critical';

    // Emit health status
    this.emitSystemEvent('healthCheck', {
      systemsOnline,
      systemsOffline,
      overallHealth,
      healthScore
    });
  }

  private emitSystemEvent(type: string, data: any): void {
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('floraAISystemEvent', {
        detail: { type, data, timestamp: Date.now() }
      });
      window.dispatchEvent(event);
    }
  }

  // Public API methods
  async processQuery(query: string, context?: any): Promise<any> {
    if (!this.isInitialized) {
      throw new Error('Flora AI Core not initialized');
    }

    try {
      // Get current context
      const currentContext = contextEngine.getCurrentContext();
      const meetingContext = currentContext ? {
        type: currentContext.meetingState.type,
        phase: currentContext.meetingState.phase,
        participants: currentContext.meetingState.participants.length,
        duration: currentContext.meetingState.duration,
        isRecording: currentContext.meetingState.isRecording,
        isScreenSharing: currentContext.meetingState.isScreenSharing,
        platform: currentContext.meetingState.platform,
        urgency: currentContext.meetingState.urgencyLevel,
        keywords: currentContext.screenData?.screenContent.keywords || []
      } : {
        type: 'unknown' as const,
        phase: 'discussion' as const,
        participants: 1,
        duration: 0,
        isRecording: false,
        isScreenSharing: false,
        platform: 'unknown',
        urgency: 'low' as const,
        keywords: []
      };

      // Process with AI orchestrator
      const response = await aiOrchestrator.processIntelligently(query, meetingContext);
      
      return response;
    } catch (error) {
      console.error('Query processing failed:', error);
      throw error;
    }
  }

  getSystemStatus(): FloraAIStatus {
    const systemsOnline = Array.from(this.systemStatus.entries())
      .filter(([, status]) => status)
      .map(([system]) => system);

    const systemsOffline = Array.from(this.systemStatus.entries())
      .filter(([, status]) => !status)
      .map(([system]) => system);

    const healthScore = systemsOnline.length / this.systemStatus.size;
    let overallHealth: FloraAIStatus['overallHealth'];

    if (healthScore >= 0.9) overallHealth = 'excellent';
    else if (healthScore >= 0.7) overallHealth = 'good';
    else if (healthScore >= 0.5) overallHealth = 'fair';
    else if (healthScore >= 0.3) overallHealth = 'poor';
    else overallHealth = 'critical';

    // Get threat count from stealth manager
    const stealthStatus = stealthManager.getStealthStatus();
    const activeThreats = stealthStatus.threatsDetected.length;

    // Get performance score
    const performanceReport = performanceOptimizer.getPerformanceReport();
    const performanceScore = Math.round((1 - performanceReport.errorRate) * 100);

    // Get security score
    const securityStatus = securityFramework.getSecurityStatus();
    const securityScore = securityStatus.isInitialized ? 95 : 0;

    return {
      isInitialized: this.isInitialized,
      systemsOnline,
      systemsOffline,
      overallHealth,
      lastUpdate: Date.now(),
      activeThreats,
      performanceScore,
      securityScore
    };
  }

  getCapabilities(): FloraAICapabilities {
    return {
      audioProcessing: {
        speechToText: this.systemStatus.get('audio') || false,
        questionDetection: this.systemStatus.get('audio') || false,
        speakerIdentification: this.systemStatus.get('audio') || false,
        sentimentAnalysis: this.systemStatus.get('audio') || false
      },
      screenAnalysis: {
        ocrCapability: this.systemStatus.get('screen') || false,
        meetingDetection: this.systemStatus.get('screen') || false,
        contentAnalysis: this.systemStatus.get('screen') || false,
        uiElementDetection: this.systemStatus.get('screen') || false
      },
      aiProcessing: {
        multiModelSupport: this.systemStatus.get('ai') || false,
        contextAwareness: this.systemStatus.get('context') || false,
        predictiveResponses: this.systemStatus.get('context') || false,
        intelligentRouting: this.systemStatus.get('ai') || false
      },
      stealth: {
        invisibilityLayers: this.systemStatus.get('stealth') ? 17 : 0,
        threatDetection: this.systemStatus.get('stealth') || false,
        dynamicPositioning: this.systemStatus.get('stealth') || false,
        processObfuscation: this.systemStatus.get('stealth') || false
      },
      security: {
        encryptionLevel: this.config.securityLevel,
        privacyCompliance: this.systemStatus.get('security') || false,
        auditLogging: this.systemStatus.get('security') || false,
        dataProtection: this.systemStatus.get('security') || false
      },
      performance: {
        streamingResponses: this.systemStatus.get('performance') || false,
        adaptiveQuality: this.systemStatus.get('performance') || false,
        resourceOptimization: this.systemStatus.get('performance') || false,
        cacheOptimization: this.systemStatus.get('performance') || false
      }
    };
  }

  async emergencyShutdown(): Promise<void> {
    console.log('🚨 Emergency shutdown initiated');
    
    try {
      // Hide from all threats immediately
      if (this.systemStatus.get('stealth')) {
        await stealthManager.emergencyHide();
      }

      // Stop all monitoring
      if (this.systemStatus.get('audio')) {
        audioProcessor.stopMonitoring();
      }

      if (this.systemStatus.get('screen')) {
        screenMonitor.stopMonitoring();
      }

      if (this.systemStatus.get('context')) {
        contextEngine.stopContextMonitoring();
      }

      // Clear all data
      if (this.systemStatus.get('security')) {
        await securityFramework.handleDataSubjectRequest('erasure');
      }

      if (this.systemStatus.get('performance')) {
        performanceOptimizer.clearCache();
      }

      console.log('✅ Emergency shutdown completed');
    } catch (error) {
      console.error('Emergency shutdown failed:', error);
    }
  }

  async destroy(): Promise<void> {
    console.log('🌸 Shutting down Flora AI Core...');

    if (this.healthCheckInterval) {
      clearInterval(this.healthCheckInterval);
    }

    // Gracefully shutdown all systems
    audioProcessor.stopMonitoring();
    screenMonitor.stopMonitoring();
    contextEngine.stopContextMonitoring();
    stealthManager.destroy();
    securityFramework.destroy();
    performanceOptimizer.destroy();

    this.systemStatus.clear();
    this.isInitialized = false;

    console.log('✅ Flora AI Core shutdown complete');
  }

  // Configuration methods
  updateConfiguration(config: Partial<FloraAIConfig>): void {
    this.config = { ...this.config, ...config };
    
    // Apply configuration changes to active systems
    if (this.isInitialized) {
      this.applyConfigurationChanges(config);
    }
  }

  private applyConfigurationChanges(config: Partial<FloraAIConfig>): void {
    if (config.stealthLevel && this.systemStatus.get('stealth')) {
      stealthManager.setStealthLevel(config.stealthLevel);
    }

    if (config.securityLevel && this.systemStatus.get('security')) {
      securityFramework.updateSecurityConfiguration({
        encryptionLevel: config.securityLevel
      });
    }

    if (config.performanceMode && this.systemStatus.get('performance')) {
      performanceOptimizer.updateSettings({
        priorityMode: config.performanceMode
      });
    }
  }

  getConfiguration(): FloraAIConfig {
    return { ...this.config };
  }
}

// Export singleton instance
export const floraAICore = new FloraAICore();

// Export for easy access
export default floraAICore;
