const { app, BrowserWindow, ipcMain, desktopCapturer, dialog, globalShortcut } = require('electron');
const path = require('path');
const { screen } = require('electron');

let mainWindow; // Store window reference for IPC

async function createWindow() {
  const { default: isDev } = await import('electron-is-dev');
  // Create invisible overlay window - True desktop transparency
  const win = new BrowserWindow({
    alwaysOnTop: true,
    width: 600,
    height: 450,
    minWidth: 500,
    maxWidth: 800,
    minHeight: 120,
    maxHeight: 500,
    webPreferences: {
      nodeIntegration: false,
      contextIsolation: true,
      enableRemoteModule: false,
      preload: path.join(__dirname, 'preload.js'),
      backgroundThrottling: false,
      // Overlay-specific settings
      offscreen: false,
      paintWhenInitiallyHidden: false,
      disableHtmlFullscreenWindowResize: true,
    },
    // Complete transparency configuration
    transparent: true,
    frame: false,
    resizable: false,
    skipTaskbar: true,
    show: false,
    movable: true,
    hasShadow: false,
    webSecurity: true,
    allowRunningInsecureContent: false,
    // Enhanced stealth properties
    titleBarStyle: 'hidden',
    roundedCorners: false,
    // Desktop transparency
    type: 'toolbar',
    focusable: true,
    acceptFirstMouse: false,
    backgroundColor: '#00000000', // Fully transparent background
  });

  mainWindow = win;

      // Position the window at the very top center
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width: screenWidth } = primaryDisplay.bounds;
    win.setPosition(Math.floor((screenWidth - 600) / 2), 0);

    // Ensure window size doesn't change
    win.setSize(600, 450);
    win.setResizable(false);

  const startUrl = isDev
    ? 'http://localhost:3000'
    : `file://${path.join(__dirname, '../out/index.html')}`;

  // Remove content protection that might interfere with transparency
  // win.setContentProtection(true);
  win.loadURL(startUrl);

  // Initialize invisible overlay with complete transparency
  win.once('ready-to-show', () => {
    win.show();
    
    // Configure as completely transparent overlay
    win.setSize(600, 450);
    win.setMovable(true);
    win.setSkipTaskbar(true);
    win.setAlwaysOnTop(true);
    
    // Ensure window is positioned at very top after showing
    const primaryDisplay = screen.getPrimaryDisplay();
    const { width: screenWidth } = primaryDisplay.bounds;
    win.setPosition(Math.floor((screenWidth - 600) / 2), 0);
    
    // Set completely transparent background
    win.setBackgroundColor('#00000000'); // Fully transparent
    win.setOpacity(1.0); // Full opacity for visibility
    
    // Critical: Set as overlay that screen capture ignores
    win.setVisibleOnAllWorkspaces(true);
    
    // Platform-specific transparent overlay setup
    try {
      if (process.platform === 'win32') {
        // Windows: Make it behave like a transparent overlay
        const { nativeImage } = require('electron');
        
        // Remove from Alt+Tab and taskbar completely
        win.setSkipTaskbar(true);
        
        // Make it behave like a system overlay
        win.setAppDetails({
          appId: 'system.overlay',
          appIconPath: '',
          appIconIndex: 0,
          relaunchCommand: '',
          relaunchDisplayName: '',
        });
        
        // Set as tool window type
        win.setThumbarButtons([]);
      }
      
      // macOS specific overlay settings
      if (process.platform === 'darwin') {
        win.setVisibleOnAllWorkspaces(true);
        win.setFullScreenable(false);
      }
    } catch (error) {
      console.log('Platform overlay setup:', error);
    }
    
    // Register global keyboard shortcuts for window movement
    registerKeyboardControls();
  });

  // Open the DevTools in development with proper positioning
  if (isDev) {
    win.webContents.openDevTools({ mode: 'detach' });
    
    // Set a solid background for development to prevent desktop bleed-through
    win.webContents.executeJavaScript(`
      document.body.style.background = '#000000';
      document.documentElement.style.background = '#000000';
    `);
  }
}

// Register keyboard controls for window movement
function registerKeyboardControls() {
  const moveDistance = 20; // pixels to move per key press
  
  try {
    // Unregister any existing shortcuts first
    globalShortcut.unregisterAll();
    
    // Register global shortcuts for window movement - try multiple combinations
    const leftRegistered = globalShortcut.register('CommandOrControl+Shift+Left', () => {
      console.log('Left arrow pressed');
      moveWindowByKeyboard(-moveDistance, 0);
    });
    
    const rightRegistered = globalShortcut.register('CommandOrControl+Shift+Right', () => {
      console.log('Right arrow pressed');
      moveWindowByKeyboard(moveDistance, 0);
    });
    
    // Disable vertical movement - only allow horizontal movement
    const upRegistered = false; // Disabled vertical movement
    const downRegistered = false; // Disabled vertical movement
    
    // Try alternative shortcuts if the main ones fail
    if (!upRegistered) {
      console.log('Trying alternative up shortcut...');
      const altUpRegistered = globalShortcut.register('CommandOrControl+Alt+Up', () => {
        console.log('Alternative up arrow pressed');
        moveWindowByKeyboard(0, -moveDistance);
      });
      console.log(`Alternative up shortcut registered: ${altUpRegistered}`);
    }
    
    // Add a test shortcut to verify global shortcuts work
    const testRegistered = globalShortcut.register('CommandOrControl+Shift+T', () => {
      console.log('Test shortcut works! Global shortcuts are functioning.');
      if (mainWindow) {
        mainWindow.webContents.executeJavaScript(`
          console.log('Test shortcut triggered from main process');
        `);
      }
    });
    console.log(`Test shortcut (Ctrl+Shift+T) registered: ${testRegistered}`);
    
    console.log('Keyboard controls registration status:');
    console.log(`Left: ${leftRegistered}, Right: ${rightRegistered}, Up: ${upRegistered}, Down: ${downRegistered}`);
    
    if (leftRegistered && rightRegistered && upRegistered && downRegistered) {
      console.log('All keyboard controls registered successfully: Ctrl+Shift+Arrow keys to move window');
    } else {
      console.error('Some keyboard shortcuts failed to register');
    }
  } catch (error) {
    console.error('Failed to register keyboard shortcuts:', error);
  }
}

// Move window using keyboard controls - HORIZONTAL ONLY
function moveWindowByKeyboard(deltaX, deltaY) {
  try {
    if (!mainWindow) {
      console.log('No main window available for movement');
      return;
    }

    const [currentX, currentY] = mainWindow.getPosition();
    // Only allow horizontal movement - ignore deltaY
    const newX = currentX + deltaX;
    const newY = currentY; // Keep Y position fixed

    console.log(`Moving window horizontally from (${currentX}, ${currentY}) to (${newX}, ${newY})`);

    // Get screen dimensions for boundary checking
    const { width: screenWidth } = screen.getPrimaryDisplay().bounds;

    // Allow horizontal movement with boundaries, keep Y position fixed
    const clampedX = Math.max(-100, Math.min(newX, screenWidth + 100));
    const clampedY = currentY; // Maintain current Y position

    mainWindow.setPosition(clampedX, clampedY);
    console.log(`Window moved horizontally to: ${clampedX}, ${clampedY}`);
  } catch (error) {
    console.error('Error moving window with keyboard:', error);
  }
}

// IPC handlers for window control - HORIZONTAL ONLY
ipcMain.handle('move-window', async (event, deltaX, deltaY) => {
  try {
    if (!mainWindow) return null;
    const [currentX, currentY] = mainWindow.getPosition();
    // Only allow horizontal movement - ignore deltaY
    const newX = currentX + deltaX;
    const newY = currentY; // Keep Y position fixed

    // Get screen dimensions for boundary checking
    const { width: screenWidth } = screen.getPrimaryDisplay().bounds;

    // Allow horizontal movement with boundaries, keep Y position fixed
    const clampedX = Math.max(-100, Math.min(newX, screenWidth + 100));
    const clampedY = currentY; // Maintain current Y position

    mainWindow.setPosition(clampedX, clampedY);
    return { x: clampedX, y: clampedY };
  } catch (error) {
    console.error('Error moving window:', error);
    return null;
  }
});

ipcMain.handle('get-window-position', async () => {
  try {
    if (!mainWindow) return [0, 0];
    return mainWindow.getPosition();
  } catch (error) {
    console.error('Error getting window position:', error);
    return [0, 0];
  }
});

ipcMain.handle('set-window-position', async (event, x, y) => {
  try {
    if (!mainWindow) return null;
    const [currentX, currentY] = mainWindow.getPosition();
    const { width: screenWidth } = screen.getPrimaryDisplay().bounds;

    // Only allow horizontal positioning - ignore Y parameter
    const clampedX = Math.max(-100, Math.min(x, screenWidth + 100));
    const clampedY = currentY; // Maintain current Y position

    mainWindow.setPosition(clampedX, clampedY);
    return { x: clampedX, y: clampedY };
  } catch (error) {
    console.error('Error setting window position:', error);
    return null;
  }
});

// Screen capture IPC handler
ipcMain.handle('get-screen-sources', async () => {
  try {
    const sources = await desktopCapturer.getSources({
      types: ['window', 'screen'],
      thumbnailSize: { width: 1920, height: 1080 }
    });
    return sources;
  } catch (error) {
    console.error('Error getting screen sources:', error);
    return [];
  }
});

// File upload dialog
ipcMain.handle('show-file-dialog', async () => {
  try {
    if (!mainWindow) return { canceled: true };
    const result = await dialog.showOpenDialog(mainWindow, {
      properties: ['openFile'],
      filters: [
        { name: 'Images', extensions: ['jpg', 'png', 'gif', 'bmp', 'webp'] },
        { name: 'All Files', extensions: ['*'] }
      ]
    });
    return result;
  } catch (error) {
    console.error('Error showing file dialog:', error);
    return { canceled: true };
  }
});

app.whenReady().then(createWindow);

app.on('window-all-closed', () => {
  // Unregister all global shortcuts
  globalShortcut.unregisterAll();
  
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

app.on('activate', () => {
  if (BrowserWindow.getAllWindows().length === 0) {
    createWindow();
  }
});

app.on('will-quit', () => {
  // Unregister all global shortcuts
  globalShortcut.unregisterAll();
});
