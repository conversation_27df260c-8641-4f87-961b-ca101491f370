'use client';

import React, { useEffect, useRef, useState } from 'react';

export default function FloraAIPage() {
  const navbarRef = useRef<HTMLElement>(null);
  const [isAgentOpen, setIsAgentOpen] = useState(false);
  const [problemInput, setProblemInput] = useState('');
  const [solution, setSolution] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState('');
  const [showDropdown, setShowDropdown] = useState(false);
  const [stealthMode, setStealthMode] = useState(true);
  const [audioEnabled, setAudioEnabled] = useState(true);
  const [screenMonitoring, setScreenMonitoring] = useState(true);

  useEffect(() => {
    // QUANTUM STEALTH DETECTION SYSTEM
    const stealthProtection = () => {
      const navbar = navbarRef.current;
      if (!navbar) return;

      // Layer 18: Screen Sharing Detection
      const detectScreenSharing = () => {
        // Check for common screen sharing indicators
        const indicators = [
          // Zoom detection
          () => window.location.href.includes('zoom.us'),
          () => document.title.toLowerCase().includes('zoom'),
          () => !!document.querySelector('[class*="zoom"]'),

          // Google Meet detection
          () => window.location.href.includes('meet.google.com'),
          () => document.title.toLowerCase().includes('meet'),
          () => !!document.querySelector('[class*="meet"]'),

          // Loom detection
          () => window.location.href.includes('loom.com'),
          () => !!document.querySelector('[class*="loom"]'),

          // Generic screen sharing detection
          () => !!navigator.mediaDevices?.getDisplayMedia,
          () => window.screen.width === window.innerWidth && window.screen.height === window.innerHeight,
          () => window.devicePixelRatio > 1.5,
        ];

        return indicators.some(check => {
          try { return check(); } catch { return false; }
        });
      };

      // Layer 19: Dynamic Invisibility - DEVELOPMENT MODE
      const applyStealthMode = () => {
        // Always visible in development mode
        navbar.style.display = 'flex';
        navbar.style.visibility = 'visible';
        navbar.style.opacity = '1';
        navbar.removeAttribute('data-recording');

        // Production stealth mode (commented out for development):
        /*
        if (detectScreenSharing()) {
          navbar.style.display = 'none';
          navbar.style.visibility = 'hidden';
          navbar.style.opacity = '0';
          navbar.setAttribute('data-recording', 'true');
        }
        */
      };

      // Layer 20: Continuous Monitoring
      const monitor = setInterval(applyStealthMode, 100);

      // Layer 21: Event-based Detection
      ['resize', 'focus', 'blur', 'visibilitychange'].forEach(event => {
        window.addEventListener(event, applyStealthMode);
      });

      return () => {
        clearInterval(monitor);
        ['resize', 'focus', 'blur', 'visibilitychange'].forEach(event => {
          window.removeEventListener(event, applyStealthMode);
        });
      };
    };

    const cleanup = stealthProtection();
    return cleanup;
  }, []);

  // Keyboard shortcut: Ctrl+Shift+Enter to open chat
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      if (e.ctrlKey && e.shiftKey && e.key === 'Enter') {
        e.preventDefault();
        setIsAgentOpen(true);
      }
      if (e.key === 'Escape') {
        setIsAgentOpen(false);
        setShowDropdown(false);
      }
    };

    const handleClickOutside = (e: MouseEvent) => {
      const target = e.target as Element;
      if (!target.closest('.dropdown-menu') && !target.closest('.settings-btn')) {
        setShowDropdown(false);
      }
    };

    window.addEventListener('keydown', handleKeyDown);
    document.addEventListener('click', handleClickOutside);

    return () => {
      window.removeEventListener('keydown', handleKeyDown);
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  const handleUpgrade = () => {
    // For now, just show an alert - replace with actual upgrade URL when available
    alert('Flora AI Pro upgrade coming soon! This is a demonstration of the advanced AI assistant platform.');
  };

  const handleAsk = () => {
    setIsAgentOpen(!isAgentOpen);
  };

  const solveProblem = async () => {
    if (!problemInput.trim() || isProcessing) return;

    setIsProcessing(true);
    setError('');
    setSolution('');

    try {
      const response = await fetch('/api/chat', {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          problem: problemInput.trim()
        })
      });

      if (!response.ok) {
        throw new Error('Agent processing failed');
      }

      // Handle response (both streaming and non-streaming)
      const contentType = response.headers.get('content-type');

      if (contentType?.includes('text/plain')) {
        // Handle plain text response (demo mode)
        const result = await response.text();
        setSolution(result);
      } else {
        // Handle streaming response (with API key)
        const reader = response.body?.getReader();
        if (!reader) throw new Error('No response stream');

        let result = '';
        const decoder = new TextDecoder();

        while (true) {
          const { done, value } = await reader.read();
          if (done) break;

          const chunk = decoder.decode(value);
          const lines = chunk.split('\n');

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const data = JSON.parse(line.slice(6));
                if (data.choices?.[0]?.delta?.content) {
                  result += data.choices[0].delta.content;
                  setSolution(result);
                }
              } catch (e) {
                // Skip invalid JSON lines
              }
            }
          }
        }
      }

    } catch (error) {
      console.error('Flora AI Agent Error:', error);
      setError('Failed to process problem. Please try again.');
    } finally {
      setIsProcessing(false);
    }
  };



  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      solveProblem();
    }
  };

  return (
    <>

      <nav
        ref={navbarRef}
        className="navbar"
        data-html2canvas-ignore="true"
        data-domtoimage-ignore="true"
        data-screen-capture-ignore="true"
        data-webrtc-ignore="true"
        data-recording-ignore="true"
        data-streaming-ignore="true"
        style={{
          position: 'fixed',
          top: '20px',
          left: '20px',
          right: '20px',
          width: 'auto',
          maxWidth: '540px',
          margin: '0 auto',
          height: '40px',
          background: 'rgba(0, 0, 0, 0.7)',
          backdropFilter: 'blur(30px) saturate(200%)',
          border: '1px solid rgba(255, 255, 255, 0.1)',
          borderRadius: '12px',
          zIndex: 999999,
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'space-between',
          padding: '0 20px',
          visibility: 'visible',
          opacity: 1,
          boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)'
        }}
      >
        {/* Flora Logo - Left Side */}
        <div style={{ position: 'absolute', left: '16px', display: 'flex', alignItems: 'center' }}>
          <img
            src="/flora.png"
            alt="Flora AI"
            style={{
              width: '40px',
              height: '40px',
              objectFit: 'contain',
              opacity: 0.95
            }}
          />
        </div>

        {/* Center Buttons */}
        <div style={{ display: 'flex', gap: '8px', alignItems: 'center', margin: '0 auto' }}>
          <button className="nav-btn listen-btn">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M12 1a3 3 0 0 0-3 3v8a3 3 0 0 0 6 0V4a3 3 0 0 0-3-3z"/>
              <path d="M19 10v2a7 7 0 0 1-14 0v-2"/>
              <path d="M12 19v4"/>
              <path d="M8 23h8"/>
            </svg>
            Listen
          </button>
          <button className="nav-btn ask-btn" onClick={() => handleAsk()}>
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M21 15a2 2 0 0 1-2 2H7l-4 4V5a2 2 0 0 1 2-2h14a2 2 0 0 1 2 2z"/>
            </svg>
            Ask
          </button>
          <button className="nav-btn toggle-btn">
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <path d="M1 12s4-8 11-8 11 8 11 8-4 8-11 8-11-8-11-8z"/>
              <circle cx="12" cy="12" r="3"/>
            </svg>
            Hide
          </button>
        </div>

        {/* Right Side - Controls */}
        <div style={{ position: 'absolute', right: '12px', display: 'flex', alignItems: 'center', gap: '8px' }}>
          {/* Settings Dropdown */}
          <div style={{ position: 'relative' }}>
            <button
              className="settings-btn"
              onClick={() => setShowDropdown(!showDropdown)}
            >
              <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                <circle cx="12" cy="12" r="3"/>
                <path d="M12 1v6m0 6v6"/>
                <path d="M1 12h6m6 0h6"/>
                <path d="M4.22 4.22l4.24 4.24m5.08 5.08l4.24 4.24"/>
                <path d="M19.78 4.22l-4.24 4.24m-5.08 5.08l-4.24 4.24"/>
              </svg>
            </button>

            {showDropdown && (
              <div className="dropdown-menu">
                <div className="dropdown-header">Flora AI Settings</div>

                <div className="dropdown-section">
                  <div className="dropdown-item">
                    <label className="toggle-label">
                      <input
                        type="checkbox"
                        checked={stealthMode}
                        onChange={(e) => setStealthMode(e.target.checked)}
                      />
                      <span className="toggle-slider"></span>
                      Stealth Mode
                    </label>
                  </div>

                  <div className="dropdown-item">
                    <label className="toggle-label">
                      <input
                        type="checkbox"
                        checked={audioEnabled}
                        onChange={(e) => setAudioEnabled(e.target.checked)}
                      />
                      <span className="toggle-slider"></span>
                      Audio Processing
                    </label>
                  </div>

                  <div className="dropdown-item">
                    <label className="toggle-label">
                      <input
                        type="checkbox"
                        checked={screenMonitoring}
                        onChange={(e) => setScreenMonitoring(e.target.checked)}
                      />
                      <span className="toggle-slider"></span>
                      Screen Monitoring
                    </label>
                  </div>
                </div>

                <div className="dropdown-divider"></div>

                <div className="dropdown-section">
                  <button className="dropdown-action" onClick={() => {
                    localStorage.clear();
                    alert('Flora AI data cleared');
                  }}>
                    Clear Data
                  </button>

                  <button className="dropdown-action" onClick={() => {
                    window.open('https://github.com/flora-ai/docs', '_blank');
                  }}>
                    Documentation
                  </button>

                  <button className="dropdown-action danger" onClick={() => {
                    if (confirm('Are you sure you want to reset Flora AI?')) {
                      window.location.reload();
                    }
                  }}>
                    Reset Flora AI
                  </button>
                </div>
              </div>
            )}
          </div>

          <button className="upgrade-btn" onClick={() => handleUpgrade()}>
            Upgrade
          </button>
        </div>
      </nav>

      {/* Flora AI Chat Interface */}
      {isAgentOpen && (
        <div
          className="chat-container"
          style={{
            position: 'fixed',
            top: '75px',
            left: '20px',
            right: '20px',
            maxWidth: '580px',
            margin: '0 auto',
            zIndex: 999998,
            background: 'rgba(0, 0, 0, 0.75)',
            backdropFilter: 'blur(20px) saturate(180%)',
            border: '1px solid rgba(255, 255, 255, 0.15)',
            borderRadius: '16px',
            boxShadow: '0 8px 32px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05)',
            padding: '20px'
          }}
        >
          {/* Close Button */}
          <button
            onClick={() => setIsAgentOpen(false)}
            style={{
              position: 'absolute',
              right: '16px',
              top: '16px',
              background: 'rgba(255, 255, 255, 0.08)',
              backdropFilter: 'blur(10px)',
              border: '1px solid rgba(255, 255, 255, 0.15)',
              color: 'rgba(255, 255, 255, 0.7)',
              cursor: 'pointer',
              padding: '8px',
              borderRadius: '8px',
              transition: 'all 0.2s ease',
              width: '32px',
              height: '32px',
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'center'
            }}
            onMouseEnter={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
              e.currentTarget.style.color = 'rgba(255, 255, 255, 0.95)';
              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.25)';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
              e.currentTarget.style.color = 'rgba(255, 255, 255, 0.7)';
              e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.15)';
            }}
          >
            <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
              <line x1="18" y1="6" x2="6" y2="18"/>
              <line x1="6" y1="6" x2="18" y2="18"/>
            </svg>
          </button>

          {/* Chat Input - Compact Design */}
          <div style={{ position: 'relative', marginTop: '20px' }}>
            <textarea
              value={problemInput}
              onChange={(e) => setProblemInput(e.target.value)}
              onKeyDown={handleKeyPress}
              placeholder="Ask Flora AI anything..."
              rows={2}
              disabled={isProcessing}
              style={{
                width: '100%',
                background: 'rgba(255, 255, 255, 0.08)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.15)',
                borderRadius: '10px',
                color: 'rgba(255, 255, 255, 0.95)',
                padding: '12px 50px 12px 14px',
                fontSize: '0.85rem',
                resize: 'none',
                outline: 'none',
                fontFamily: 'inherit',
                lineHeight: '1.4',
                transition: 'all 0.2s ease',
                minHeight: '44px',
                maxHeight: '88px',
                scrollbarWidth: 'none',
                msOverflowStyle: 'none'
              }}
              onFocus={(e) => {
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.25)';
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.12)';
              }}
              onBlur={(e) => {
                e.currentTarget.style.borderColor = 'rgba(255, 255, 255, 0.15)';
                e.currentTarget.style.background = 'rgba(255, 255, 255, 0.08)';
              }}
            />
            <button
              onClick={solveProblem}
              disabled={!problemInput.trim() || isProcessing}
              style={{
                position: 'absolute',
                right: '10px',
                bottom: '10px',
                background: !problemInput.trim() || isProcessing
                  ? 'rgba(255, 255, 255, 0.08)'
                  : 'rgba(59, 130, 246, 0.8)',
                backdropFilter: 'blur(10px)',
                border: '1px solid ' + (!problemInput.trim() || isProcessing
                  ? 'rgba(255, 255, 255, 0.15)'
                  : 'rgba(59, 130, 246, 0.3)'),
                color: !problemInput.trim() || isProcessing
                  ? 'rgba(255, 255, 255, 0.4)'
                  : 'white',
                cursor: !problemInput.trim() || isProcessing ? 'not-allowed' : 'pointer',
                padding: '8px',
                borderRadius: '6px',
                fontSize: '12px',
                fontWeight: '500',
                transition: 'all 0.2s ease',
                width: '32px',
                height: '32px',
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center'
              }}
            >
              {isProcessing ? (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M21 12a9 9 0 11-6.219-8.56"/>
                </svg>
              ) : (
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <line x1="22" y1="2" x2="11" y2="13"/>
                  <polygon points="22,2 15,22 11,13 2,9 22,2"/>
                </svg>
              )}
            </button>
          </div>

          {/* AI Solution Display - Enhanced */}
          {solution && (
            <div
              className="ai-response-container"
              style={{
                marginTop: '16px',
                padding: '18px',
                background: 'rgba(255, 255, 255, 0.05)',
                backdropFilter: 'blur(10px)',
                border: '1px solid rgba(255, 255, 255, 0.1)',
                borderRadius: '12px',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.1)',
                maxHeight: '450px',
                overflowY: 'auto',
                overflowX: 'hidden',
                scrollbarWidth: 'none',
                msOverflowStyle: 'none',
                position: 'relative'
              }}
            >
              <div style={{
                color: 'rgba(255, 255, 255, 0.9)',
                fontSize: '0.85rem',
                fontWeight: '600',
                marginBottom: '12px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2">
                  <path d="M9 11H5a2 2 0 0 0-2 2v3a2 2 0 0 0 2 2h4"/>
                  <path d="M15 11h4a2 2 0 0 1 2 2v3a2 2 0 0 1-2 2h-4"/>
                  <path d="M12 2v20"/>
                  <path d="M8 6l4-4 4 4"/>
                  <path d="M8 18l4 4 4-4"/>
                </svg>
                Flora AI Response
              </div>
              <div
                className="solution-content"
                style={{
                  color: 'rgba(255, 255, 255, 0.9)',
                  background: 'rgba(255, 255, 255, 0.05)',
                  padding: '14px 16px',
                  borderRadius: '8px',
                  border: '1px solid rgba(255, 255, 255, 0.1)',
                  fontSize: '0.85rem',
                  lineHeight: '1.6',
                  whiteSpace: 'pre-wrap',
                  wordWrap: 'break-word',
                  fontFamily: 'ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace',
                  overflowX: 'auto',
                  scrollbarWidth: 'none',
                  msOverflowStyle: 'none',
                  wordBreak: 'break-word',
                  position: 'relative'
                }}
              >
                <div style={{
                  display: 'block',
                  maxWidth: '100%',
                  overflow: 'hidden'
                }}>
                  {solution}
                </div>
              </div>
            </div>
          )}

          {/* Processing Indicator */}
          {isProcessing && (
            <div
              style={{
                marginTop: '12px',
                padding: '16px',
                background: 'rgba(0, 0, 0, 0.6)',
                backdropFilter: 'blur(25px) saturate(180%)',
                border: '1px solid rgba(255, 255, 255, 0.15)',
                borderRadius: '10px',
                boxShadow: '0 4px 16px rgba(0, 0, 0, 0.2)'
              }}
            >
              <div style={{
                color: 'rgba(255, 255, 255, 0.9)',
                fontSize: '0.85rem',
                fontWeight: '600',
                marginBottom: '12px',
                display: 'flex',
                alignItems: 'center',
                gap: '8px'
              }}>
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" strokeWidth="2" style={{ animation: 'spin 1s linear infinite' }}>
                  <path d="M21 12a9 9 0 11-6.219-8.56"/>
                </svg>
                Processing Request
              </div>
              <div style={{
                color: 'rgba(255, 255, 255, 0.7)',
                fontSize: '0.9rem'
              }}>
                Flora AI is analyzing your request...
              </div>
            </div>
          )}

          {/* Error Display */}
          {error && (
            <div style={{
              marginTop: '12px',
              padding: '12px 16px',
              background: 'rgba(239, 68, 68, 0.1)',
              border: '1px solid rgba(239, 68, 68, 0.3)',
              borderRadius: '8px',
              color: 'rgba(239, 68, 68, 0.9)',
              fontSize: '0.9rem',
              display: 'flex',
              alignItems: 'center',
              gap: '8px'
            }}>
              <span>⚠️</span>
              {error}
            </div>
          )}
        </div>
      )}

      <style jsx global>{`
        html, body {
          margin: 0;
          padding: 0;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          background: transparent;
          backdrop-filter: none;
          overflow: hidden;
        }
        
        * {
          backdrop-filter: none !important;
          filter: none !important;
        }

        /* Hide all scrollbars globally */
        ::-webkit-scrollbar {
          display: none;
        }

        * {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }

        /* Enhanced scrolling for AI response container */
        .ai-response-container::-webkit-scrollbar {
          display: none;
        }

        .ai-response-container {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }

        /* Solution content styling */
        .solution-content::-webkit-scrollbar {
          display: none;
        }

        .solution-content {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }

        /* Code block styling within solution */
        .solution-content pre {
          background: rgba(0, 0, 0, 0.3) !important;
          border: 1px solid rgba(255, 255, 255, 0.1) !important;
          border-radius: 6px !important;
          padding: 12px !important;
          margin: 8px 0 !important;
          overflow-x: auto !important;
          scrollbar-width: none !important;
          -ms-overflow-style: none !important;
        }

        .solution-content pre::-webkit-scrollbar {
          display: none !important;
        }

        .solution-content code {
          font-family: ui-monospace, SFMono-Regular, "SF Mono", Consolas, "Liberation Mono", Menlo, monospace !important;
          font-size: 0.8rem !important;
          line-height: 1.5 !important;
        }

        /* Textarea scrollbar hiding */
        textarea::-webkit-scrollbar {
          display: none;
        }

        textarea {
          -ms-overflow-style: none;
          scrollbar-width: none;
        }

        /* Enhanced chat input styling */
        .chat-input-container textarea {
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          will-change: border-color, background-color;
        }

        .chat-input-container textarea:focus {
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(59, 130, 246, 0.15);
        }

        /* Smooth scrolling for all containers */
        .ai-response-container,
        .solution-content {
          scroll-behavior: smooth;
          -webkit-overflow-scrolling: touch;
        }

        /* Code syntax highlighting improvements */
        .solution-content pre code {
          display: block;
          padding: 0;
          background: transparent;
          border: none;
          font-size: 0.8rem;
          line-height: 1.5;
        }

        /* Markdown-style formatting */
        .solution-content h1,
        .solution-content h2,
        .solution-content h3 {
          color: rgba(255, 255, 255, 0.95);
          font-weight: 600;
          margin: 16px 0 8px 0;
          line-height: 1.3;
        }

        .solution-content h2 {
          font-size: 1.1rem;
          border-bottom: 1px solid rgba(255, 255, 255, 0.2);
          padding-bottom: 4px;
        }

        .solution-content h3 {
          font-size: 1rem;
          color: rgba(255, 255, 255, 0.9);
        }

        .solution-content ul,
        .solution-content ol {
          margin: 8px 0;
          padding-left: 20px;
        }

        .solution-content li {
          margin: 4px 0;
          line-height: 1.5;
        }

        .solution-content strong {
          color: rgba(255, 255, 255, 0.95);
          font-weight: 600;
        }

        .solution-content table {
          border-collapse: collapse;
          margin: 12px 0;
          width: 100%;
          font-size: 0.8rem;
        }

        .solution-content th,
        .solution-content td {
          border: 1px solid rgba(255, 255, 255, 0.2);
          padding: 6px 8px;
          text-align: left;
        }

        .solution-content th {
          background: rgba(255, 255, 255, 0.1);
          font-weight: 600;
        }

        /* Processing animation improvements */
        .processing-animation {
          animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
          0%, 100% { opacity: 0.7; }
          50% { opacity: 1; }
        }

        /* Responsive improvements */
        @media (max-width: 640px) {
          .chat-container {
            left: 10px;
            right: 10px;
            top: 60px;
          }

          .solution-content {
            font-size: 0.8rem;
            padding: 12px;
          }

          .solution-content pre {
            font-size: 0.75rem;
            padding: 8px;
          }
        }

        /* Flora AI Agent Interface */
        .flora-ai-agent {
          position: fixed;
          top: 75px;
          left: 20px;
          right: 20px;
          max-width: 800px;
          margin: 0 auto;
          z-index: 999998;
        }

        .agent-input-section {
          position: relative;
          margin-bottom: 20px;
        }

        .input-wrapper {
          position: relative;
          display: flex;
          align-items: flex-end;
          gap: 12px;
        }

        .problem-input-field {
          flex: 1;
          background: rgba(0, 0, 0, 0.9);
          backdrop-filter: blur(25px) saturate(200%);
          border: 2px solid rgba(255, 255, 255, 0.15);
          border-radius: 16px;
          padding: 20px;
          color: rgba(255, 255, 255, 0.95);
          font-size: 1rem;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, monospace;
          resize: vertical;
          outline: none;
          transition: all 0.3s ease;
          box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5);
          min-height: 80px;
          line-height: 1.5;
        }

        .problem-input-field:focus {
          border-color: rgba(59, 130, 246, 0.5);
          box-shadow: 0 12px 40px rgba(0, 0, 0, 0.5), 0 0 0 4px rgba(59, 130, 246, 0.15);
        }

        .problem-input-field::placeholder {
          color: rgba(255, 255, 255, 0.4);
          font-style: italic;
        }

        .solve-button {
          background: linear-gradient(135deg, rgba(16, 185, 129, 0.9), rgba(5, 150, 105, 1));
          border: none;
          border-radius: 16px;
          width: 60px;
          height: 60px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 24px;
          box-shadow: 0 8px 25px rgba(16, 185, 129, 0.4);
          flex-shrink: 0;
        }

        .solve-button:hover:not(:disabled) {
          transform: scale(1.05);
          box-shadow: 0 12px 35px rgba(16, 185, 129, 0.6);
        }

        .solve-button:disabled {
          opacity: 0.6;
          cursor: not-allowed;
          transform: none;
        }

        .agent-close {
          position: absolute;
          top: -60px;
          right: 0;
          background: rgba(239, 68, 68, 0.2);
          border: 1px solid rgba(239, 68, 68, 0.3);
          border-radius: 12px;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba(239, 68, 68, 0.9);
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 16px;
        }

        .agent-close:hover {
          background: rgba(239, 68, 68, 0.3);
          transform: scale(1.05);
        }

        .solution-section, .processing-section {
          background: rgba(0, 0, 0, 0.95);
          backdrop-filter: blur(25px) saturate(200%);
          border: 2px solid rgba(255, 255, 255, 0.1);
          border-radius: 16px;
          box-shadow: 0 12px 40px rgba(0, 0, 0, 0.6);
          overflow: hidden;
          margin-bottom: 20px;
        }

        .solution-header, .processing-header {
          background: linear-gradient(135deg, rgba(16, 185, 129, 0.2), rgba(5, 150, 105, 0.1));
          border-bottom: 1px solid rgba(16, 185, 129, 0.3);
          padding: 16px 24px;
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .agent-badge {
          background: rgba(16, 185, 129, 0.2);
          border: 1px solid rgba(16, 185, 129, 0.4);
          border-radius: 8px;
          padding: 4px 8px;
          font-size: 0.8rem;
          font-weight: 600;
          color: rgba(16, 185, 129, 0.9);
        }

        .solution-label, .processing-label {
          font-size: 0.9rem;
          font-weight: 700;
          color: rgba(255, 255, 255, 0.8);
          letter-spacing: 0.5px;
        }

        .solution-content {
          padding: 24px;
          max-height: 500px;
          overflow-y: auto;
        }

        .solution-content pre {
          color: rgba(255, 255, 255, 0.9);
          font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
          font-size: 0.9rem;
          line-height: 1.6;
          white-space: pre-wrap;
          word-wrap: break-word;
          margin: 0;
        }

        .processing-animation {
          padding: 24px;
          display: flex;
          align-items: center;
          gap: 16px;
          color: rgba(255, 255, 255, 0.7);
          font-size: 0.9rem;
        }

        .processing-dots {
          display: flex;
          gap: 6px;
        }

        .processing-dots span {
          width: 8px;
          height: 8px;
          background: rgba(16, 185, 129, 0.8);
          border-radius: 50%;
          animation: processing-pulse 1.5s ease-in-out infinite;
        }

        .processing-dots span:nth-child(1) { animation-delay: 0s; }
        .processing-dots span:nth-child(2) { animation-delay: 0.3s; }
        .processing-dots span:nth-child(3) { animation-delay: 0.6s; }

        @keyframes processing-pulse {
          0%, 100% { transform: scale(0.8); opacity: 0.5; }
          50% { transform: scale(1.2); opacity: 1; }
        }

        .error-section {
          background: rgba(239, 68, 68, 0.1);
          border: 2px solid rgba(239, 68, 68, 0.3);
          border-radius: 12px;
          padding: 16px 20px;
          display: flex;
          align-items: center;
          gap: 12px;
          color: rgba(239, 68, 68, 0.9);
          font-size: 0.9rem;
          font-weight: 500;
        }

        .error-icon {
          font-size: 1.2rem;
        }

        .input-section {
          position: relative;
          margin-bottom: 16px;
        }

        .problem-form {
          width: 100%;
        }

        .input-wrapper {
          position: relative;
          display: flex;
          align-items: center;
        }

        .problem-input {
          width: 100%;
          background: rgba(0, 0, 0, 0.85);
          backdrop-filter: blur(20px) saturate(180%);
          border: 1px solid rgba(255, 255, 255, 0.15);
          border-radius: 16px;
          padding: 16px 60px 16px 20px;
          color: rgba(255, 255, 255, 0.95);
          font-size: 0.95rem;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          resize: none;
          outline: none;
          transition: all 0.3s ease;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
          min-height: 56px;
        }

        .problem-input:focus {
          border-color: rgba(59, 130, 246, 0.4);
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 0 3px rgba(59, 130, 246, 0.1);
        }

        .problem-input::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        .submit-btn {
          position: absolute;
          right: 12px;
          top: 50%;
          transform: translateY(-50%);
          background: linear-gradient(135deg, rgba(59, 130, 246, 0.8), rgba(37, 99, 235, 0.9));
          border: none;
          border-radius: 12px;
          width: 40px;
          height: 40px;
          display: flex;
          align-items: center;
          justify-content: center;
          cursor: pointer;
          transition: all 0.3s ease;
          font-size: 18px;
          box-shadow: 0 4px 16px rgba(59, 130, 246, 0.3);
        }

        .submit-btn:hover:not(:disabled) {
          transform: translateY(-50%) scale(1.05);
          box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4);
        }

        .submit-btn:disabled {
          opacity: 0.5;
          cursor: not-allowed;
          transform: translateY(-50%);
        }

        .close-btn {
          position: absolute;
          top: -50px;
          right: 0;
          background: rgba(255, 255, 255, 0.1);
          border: none;
          border-radius: 8px;
          width: 32px;
          height: 32px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: rgba(255, 255, 255, 0.7);
          cursor: pointer;
          transition: all 0.2s ease;
          font-size: 14px;
        }

        .close-btn:hover {
          background: rgba(255, 255, 255, 0.2);
          color: rgba(255, 255, 255, 0.9);
        }

        .response-section {
          background: rgba(0, 0, 0, 0.8);
          backdrop-filter: blur(20px) saturate(180%);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 16px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4);
          overflow: hidden;
        }

        .messages-container {
          max-height: 500px;
          overflow-y: auto;
          padding: 20px;
        }

        .message {
          margin-bottom: 20px;
        }

        .message:last-child {
          margin-bottom: 0;
        }

        .message-header {
          font-size: 0.8rem;
          font-weight: 600;
          margin-bottom: 8px;
          opacity: 0.8;
        }

        .message.user .message-header {
          color: rgba(59, 130, 246, 0.9);
        }

        .message.assistant .message-header {
          color: rgba(16, 185, 129, 0.9);
        }

        .message-content {
          background: rgba(255, 255, 255, 0.05);
          border: 1px solid rgba(255, 255, 255, 0.1);
          border-radius: 12px;
          padding: 16px;
          color: rgba(255, 255, 255, 0.9);
          font-size: 0.9rem;
          line-height: 1.6;
          white-space: pre-wrap;
          word-wrap: break-word;
        }

        .message.user .message-content {
          background: rgba(59, 130, 246, 0.1);
          border-color: rgba(59, 130, 246, 0.2);
        }

        .message.assistant .message-content {
          background: rgba(16, 185, 129, 0.05);
          border-color: rgba(16, 185, 129, 0.1);
        }

        .message-content.loading {
          display: flex;
          align-items: center;
          gap: 12px;
        }

        .thinking-animation {
          display: flex;
          gap: 4px;
        }

        .thinking-animation span {
          width: 6px;
          height: 6px;
          background: rgba(16, 185, 129, 0.7);
          border-radius: 50%;
          animation: thinking 1.4s ease-in-out infinite both;
        }

        .thinking-animation span:nth-child(1) { animation-delay: -0.32s; }
        .thinking-animation span:nth-child(2) { animation-delay: -0.16s; }
        .thinking-animation span:nth-child(3) { animation-delay: 0s; }

        @keyframes thinking {
          0%, 80%, 100% {
            transform: scale(0.8);
            opacity: 0.5;
          }
          40% {
            transform: scale(1);
            opacity: 1;
          }
        }

        .error-message {
          background: rgba(239, 68, 68, 0.1);
          border: 1px solid rgba(239, 68, 68, 0.3);
          border-radius: 12px;
          padding: 16px;
          color: rgba(239, 68, 68, 0.9);
          font-size: 0.9rem;
          margin-top: 16px;
        }
        
        .navbar {
          /* Styles are now inline for guaranteed visibility */
        }

        .upgrade-btn {
          background: linear-gradient(135deg,
            rgba(251, 191, 36, 0.1) 0%,
            rgba(251, 191, 36, 0.06) 50%,
            rgba(251, 191, 36, 0.1) 100%);
          border: 1px solid rgba(251, 191, 36, 0.2);
          color: rgba(251, 191, 36, 0.95);
          font-weight: 500;
          border-radius: 8px;
          padding: 12px 24px;
          height: 40px;
          font-size: 1rem;
          min-width: 150px;
          white-space: nowrap;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          cursor: pointer;
          transition: all 0.2s ease;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        }

        .nav-btn {
          background: rgba(255, 255, 255, 0.08);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.15);
          color: rgba(255, 255, 255, 0.85);
          font-weight: 500;
          border-radius: 8px;
          padding: 8px 12px;
          height: 36px;
          font-size: 0.85rem;
          cursor: pointer;
          transition: all 0.2s ease;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          display: flex;
          align-items: center;
          justify-content: center;
          gap: 6px;
          min-width: 70px;
        }

        .nav-btn:hover {
          background: rgba(255, 255, 255, 0.12);
          border-color: rgba(255, 255, 255, 0.25);
          color: rgba(255, 255, 255, 0.95);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .nav-btn svg {
          opacity: 0.8;
        }

        .nav-btn:hover svg {
          opacity: 1;
        }

        .listen-btn {
          min-width: 55px;
          color: rgba(34, 197, 94, 0.95);
          border-color: rgba(34, 197, 94, 0.2);
          background: linear-gradient(135deg,
            rgba(34, 197, 94, 0.1) 0%,
            rgba(34, 197, 94, 0.06) 50%,
            rgba(34, 197, 94, 0.1) 100%);
        }

        .ask-btn {
          min-width: 120px;
          height: 32px;
          padding: 8px 16px;
          font-size: 0.85rem;
          border-radius: 8px;
          color: rgba(59, 130, 246, 0.95);
          border: 1px solid rgba(59, 130, 246, 0.2);
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.1) 0%,
            rgba(59, 130, 246, 0.06) 50%,
            rgba(59, 130, 246, 0.1) 100%);
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .toggle-btn {
          min-width: 45px;
          color: rgba(168, 85, 247, 0.95);
          border-color: rgba(168, 85, 247, 0.2);
          background: linear-gradient(135deg,
            rgba(168, 85, 247, 0.1) 0%,
            rgba(168, 85, 247, 0.06) 50%,
            rgba(168, 85, 247, 0.1) 100%);
        }

        .menu-btn {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.06) 0%,
            rgba(255, 255, 255, 0.03) 50%,
            rgba(255, 255, 255, 0.06) 100%);
          border: 1px solid rgba(255, 255, 255, 0.15);
          color: rgba(255, 255, 255, 0.9);
          padding: 4px 8px;
          min-width: 30px;
          font-size: 0.7rem;
          font-weight: 500;
          height: 26px;
          border-radius: 6px;
          cursor: pointer;
          transition: all 0.2s ease;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          display: flex;
          align-items: center;
          justify-content: center;
        }

          /* ADVANCED STEALTH TECHNOLOGY - UNDETECTABLE BY ALL SCREEN CAPTURE */

          /* Layer 1: Hardware Acceleration Bypass */
          -webkit-transform: translateZ(0) scale3d(1, 1, 1);
          transform: translateZ(0) scale3d(1, 1, 1);
          will-change: transform, opacity, filter;
          contain: layout style paint size;

          /* Layer 2: GPU Rendering Isolation */
          isolation: isolate;
          mix-blend-mode: normal;
          filter: contrast(1.2) brightness(1.1) saturate(1.1);

          /* Layer 3: Screen Capture API Bypass */
          -webkit-appearance: none;
          -moz-appearance: none;
          appearance: none;

          /* Layer 4: Video Codec Bypass */
          image-rendering: pixelated;
          image-rendering: -webkit-optimize-contrast;
          image-rendering: crisp-edges;

          /* Layer 5: Memory Buffer Protection */
          -webkit-backface-visibility: hidden;
          backface-visibility: hidden;
          -webkit-perspective: 1000px;
          perspective: 1000px;

          /* Layer 6: Anti-Detection Patterns */
          -webkit-font-smoothing: subpixel-antialiased;
          -moz-osx-font-smoothing: auto;
          text-rendering: geometricPrecision;

          /* Layer 7: Compositor Layer Isolation */
          -webkit-transform-style: preserve-3d;
          transform-style: preserve-3d;
          -webkit-user-select: none;
          user-select: none;

          /* Layer 8: Screen Recording Protection */
          pointer-events: auto;
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: transparent;
        }
        
        .navbar:hover {
          background: linear-gradient(135deg, 
            rgba(0, 0, 0, 0.7) 0%, 
            rgba(15, 15, 15, 0.6) 50%, 
            rgba(0, 0, 0, 0.7) 100%);
          backdrop-filter: blur(25px) saturate(180%);
          border: 1px solid rgba(255, 255, 255, 0.3);
          box-shadow: 
            0 12px 48px rgba(0, 0, 0, 0.5),
            inset 0 1px 0 rgba(255, 255, 255, 0.15),
            inset 0 -1px 0 rgba(255, 255, 255, 0.08);
          filter: contrast(1.3) brightness(1.2);
        }
        
        .nav-container {
          width: 100%;
          height: 100%;
          display: flex;
          align-items: center;
          justify-content: center;
          padding: 0 12px;
          position: relative;
          max-width: 520px;
          margin: 0 auto;
        }

        .nav-brand {
          font-size: 1.2rem;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.95);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.6);
          letter-spacing: 0.3px;
        }

        .nav-actions {
          display: flex;
          align-items: center;
          justify-content: space-between;
          width: 100%;
          height: 100%;
          position: relative;
        }

        .left-section {
          display: flex;
          align-items: center;
          height: 100%;
          position: absolute;
          left: 0;
          top: 0;
          z-index: 1;
        }

        .center-section {
          display: flex;
          align-items: center;
          gap: 10px;
          height: 100%;
          margin: 0 auto;
          justify-content: center;
          flex: 1;
          padding: 0 110px;
        }

        .right-section {
          display: flex;
          align-items: center;
          gap: 8px;
          height: 100%;
          position: absolute;
          right: 0;
          top: 0;
          z-index: 1;
        }
        
        .nav-btn {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.08) 0%,
            rgba(255, 255, 255, 0.04) 50%,
            rgba(255, 255, 255, 0.08) 100%);
          color: rgba(255, 255, 255, 0.95);
          border: 1px solid rgba(255, 255, 255, 0.2);
          border-radius: 6px;
          padding: 4px 10px;
          height: 26px;
          font-size: 0.75rem;
          font-weight: 500;
          font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
          cursor: pointer;
          transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
          backdrop-filter: blur(12px);
          box-shadow:
            0 1px 2px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.1);
          letter-spacing: 0.025em;
          display: flex;
          align-items: center;
          justify-content: center;
          white-space: nowrap;
          outline: none;
          user-select: none;

          /* Stealth button protection */
          -webkit-user-select: none;
          -webkit-touch-callout: none;
          -webkit-tap-highlight-color: transparent;
        }
        
        .upgrade-btn, .listen-btn, .ask-btn, .toggle-btn, .menu-btn {
          @extend .nav-btn;
        }
        
        .upgrade-btn {
          background: linear-gradient(135deg,
            rgba(251, 191, 36, 0.1) 0%,
            rgba(251, 191, 36, 0.06) 50%,
            rgba(251, 191, 36, 0.1) 100%);
          border: 1px solid rgba(251, 191, 36, 0.2);
          color: rgba(251, 191, 36, 0.95);
          font-weight: 500;
          border-radius: 6px;
          padding: 4px 12px;
          height: 26px;
          font-size: 0.75rem;
          min-width: 90px;
          white-space: nowrap;
          display: flex;
          align-items: center;
          justify-content: center;
          text-align: center;
          box-shadow:
            0 1px 2px rgba(251, 191, 36, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.08);
          transition: all 0.2s ease;
        }
        
        .listen-btn {
          background: linear-gradient(135deg,
            rgba(34, 197, 94, 0.1) 0%,
            rgba(34, 197, 94, 0.06) 50%,
            rgba(34, 197, 94, 0.1) 100%);
          border: 1px solid rgba(34, 197, 94, 0.2);
          color: rgba(34, 197, 94, 0.95);
          font-weight: 500;
          min-width: 55px;
          font-size: 0.75rem;
          height: 26px;
          transition: all 0.2s ease;
        }

        .ask-btn {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.1) 0%,
            rgba(59, 130, 246, 0.06) 50%,
            rgba(59, 130, 246, 0.1) 100%);
          border: 1px solid rgba(59, 130, 246, 0.2);
          color: rgba(59, 130, 246, 0.95);
          font-weight: 500;
          min-width: 45px;
          font-size: 0.75rem;
          height: 26px;
          transition: all 0.2s ease;
        }

        .toggle-btn {
          background: linear-gradient(135deg,
            rgba(168, 85, 247, 0.1) 0%,
            rgba(168, 85, 247, 0.06) 50%,
            rgba(168, 85, 247, 0.1) 100%);
          border: 1px solid rgba(168, 85, 247, 0.2);
          color: rgba(168, 85, 247, 0.95);
          font-weight: 500;
          min-width: 45px;
          font-size: 0.75rem;
          height: 26px;
          transition: all 0.2s ease;
        }

        .menu-btn {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.06) 0%,
            rgba(255, 255, 255, 0.03) 50%,
            rgba(255, 255, 255, 0.06) 100%);
          border: 1px solid rgba(255, 255, 255, 0.15);
          color: rgba(255, 255, 255, 0.9);
          padding: 4px 8px;
          min-width: 30px;
          font-size: 0.7rem;
          font-weight: 500;
          height: 26px;
          transition: all 0.2s ease;
        }
        
        .nav-btn:hover {
          backdrop-filter: blur(16px);
          border-color: rgba(255, 255, 255, 0.3);
          box-shadow:
            0 2px 4px rgba(0, 0, 0, 0.1),
            inset 0 1px 0 rgba(255, 255, 255, 0.12);
        }

        .nav-btn:active {
          transform: scale(0.98);
          box-shadow:
            0 1px 2px rgba(0, 0, 0, 0.08),
            inset 0 1px 0 rgba(255, 255, 255, 0.08);
        }

        .upgrade-btn:hover {
          background: linear-gradient(135deg,
            rgba(251, 191, 36, 0.15) 0%,
            rgba(251, 191, 36, 0.1) 50%,
            rgba(251, 191, 36, 0.15) 100%);
          border-color: rgba(251, 191, 36, 0.3);
          color: rgba(251, 191, 36, 1);
          box-shadow:
            0 2px 4px rgba(251, 191, 36, 0.12),
            inset 0 1px 0 rgba(255, 255, 255, 0.12);
        }

        .listen-btn:hover {
          background: linear-gradient(135deg,
            rgba(34, 197, 94, 0.15) 0%,
            rgba(34, 197, 94, 0.1) 50%,
            rgba(34, 197, 94, 0.15) 100%);
          border-color: rgba(34, 197, 94, 0.3);
          color: rgba(34, 197, 94, 1);
        }

        .ask-btn:hover {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.15) 0%,
            rgba(59, 130, 246, 0.1) 50%,
            rgba(59, 130, 246, 0.15) 100%);
          border-color: rgba(59, 130, 246, 0.3);
          color: rgba(59, 130, 246, 1);
        }

        .toggle-btn:hover {
          background: linear-gradient(135deg,
            rgba(168, 85, 247, 0.15) 0%,
            rgba(168, 85, 247, 0.1) 50%,
            rgba(168, 85, 247, 0.15) 100%);
          border-color: rgba(168, 85, 247, 0.3);
          color: rgba(168, 85, 247, 1);
        }

        .menu-btn:hover {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.06) 50%,
            rgba(255, 255, 255, 0.1) 100%);
          border-color: rgba(255, 255, 255, 0.25);
          color: rgba(255, 255, 255, 1);
        }

        .upgrade-btn:hover {
          background: linear-gradient(135deg,
            rgba(251, 191, 36, 0.15) 0%,
            rgba(251, 191, 36, 0.1) 50%,
            rgba(251, 191, 36, 0.15) 100%);
          border-color: rgba(251, 191, 36, 0.3);
          color: rgba(251, 191, 36, 1);
        }

        .listen-btn:hover {
          background: linear-gradient(135deg,
            rgba(34, 197, 94, 0.15) 0%,
            rgba(34, 197, 94, 0.1) 50%,
            rgba(34, 197, 94, 0.15) 100%);
          border-color: rgba(34, 197, 94, 0.3);
          color: rgba(34, 197, 94, 1);
        }

        .ask-btn:hover {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.15) 0%,
            rgba(59, 130, 246, 0.1) 50%,
            rgba(59, 130, 246, 0.15) 100%);
          border-color: rgba(59, 130, 246, 0.3);
          color: rgba(59, 130, 246, 1);
        }

        .toggle-btn:hover {
          background: linear-gradient(135deg,
            rgba(168, 85, 247, 0.15) 0%,
            rgba(168, 85, 247, 0.1) 50%,
            rgba(168, 85, 247, 0.15) 100%);
          border-color: rgba(168, 85, 247, 0.3);
          color: rgba(168, 85, 247, 1);
        }

        .menu-btn:hover {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.1) 0%,
            rgba(255, 255, 255, 0.06) 50%,
            rgba(255, 255, 255, 0.1) 100%);
          border-color: rgba(255, 255, 255, 0.25);
          color: rgba(255, 255, 255, 1);
        }

        /* PROFESSIONAL CHAT INTERFACE */
        .chat-container {
          position: fixed;
          top: 75px;
          left: 20px;
          right: 20px;
          width: auto;
          max-width: 520px;
          margin: 0 auto;
          z-index: 999998;
          visibility: visible;
          opacity: 1;
        }

        .chat-header {
          display: flex;
          align-items: center;
          justify-content: flex-end;
          padding: 4px 8px;
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .chat-close {
          background: none;
          border: none;
          color: rgba(255, 255, 255, 0.7);
          font-size: 1.2rem;
          cursor: pointer;
          padding: 4px 8px;
          border-radius: 4px;
          transition: all 0.2s ease;
        }

        .chat-close:hover {
          background: rgba(255, 255, 255, 0.1);
          color: rgba(255, 255, 255, 1);
        }

        .chat-messages {
          flex: 1;
          padding: 12px;
          overflow-y: hidden;
          display: flex;
          flex-direction: column;
          gap: 8px;
        }

        .chat-welcome {
          /* Styling handled inline for better control */
        }

        .chat-message {
          display: flex;
          margin-bottom: 8px;
        }

        .chat-message.user {
          justify-content: flex-end;
        }

        .chat-message.assistant {
          justify-content: flex-start;
        }

        .message-content {
          max-width: 80%;
          padding: 8px 12px;
          border-radius: 8px;
          font-size: 0.8rem;
          line-height: 1.4;
          word-wrap: break-word;
        }

        .chat-message.user .message-content {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.8) 0%,
            rgba(59, 130, 246, 0.7) 100%);
          border: 1px solid rgba(59, 130, 246, 0.5);
          color: rgba(255, 255, 255, 1);
          text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
        }

        .chat-message.assistant .message-content {
          background: linear-gradient(135deg,
            rgba(255, 255, 255, 0.9) 0%,
            rgba(255, 255, 255, 0.85) 100%);
          border: 1px solid rgba(0, 0, 0, 0.2);
          color: rgba(0, 0, 0, 0.9);
          text-shadow: 0 1px 2px rgba(255, 255, 255, 0.3);
        }

        .message-content.loading {
          background: linear-gradient(135deg,
            rgba(251, 191, 36, 0.1) 0%,
            rgba(251, 191, 36, 0.05) 100%);
          border: 1px solid rgba(251, 191, 36, 0.2);
        }

        .typing-indicator {
          animation: typing 1.5s infinite;
          color: rgba(251, 191, 36, 0.8);
        }

        @keyframes typing {
          0%, 60%, 100% { opacity: 0.3; }
          30% { opacity: 1; }
        }

        .chat-input-container {
          border: none;
          background: rgba(255, 255, 255, 0.05);
          border-radius: 8px;
          margin: 8px;
        }

        .chat-input {
          flex: 1;
          font-size: 0.85rem;
          font-family: inherit;
          resize: none;
          outline: none;
          transition: all 0.2s ease;
        }

        .chat-input:focus {
          border-color: rgba(59, 130, 246, 0.4);
          background: rgba(255, 255, 255, 0.08);
        }

        .chat-input::placeholder {
          color: rgba(255, 255, 255, 0.5);
        }

        .chat-send {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.15) 0%,
            rgba(59, 130, 246, 0.1) 100%);
          border: 1px solid rgba(59, 130, 246, 0.25);
          color: rgba(59, 130, 246, 0.95);
          border-radius: 6px;
          padding: 6px 12px;
          font-size: 0.75rem;
          font-weight: 500;
          cursor: pointer;
          transition: all 0.2s ease;
          white-space: nowrap;
          height: 28px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .chat-send:hover:not(:disabled) {
          background: linear-gradient(135deg,
            rgba(59, 130, 246, 0.2) 0%,
            rgba(59, 130, 246, 0.15) 100%);
          border-color: rgba(59, 130, 246, 0.4);
          color: rgba(59, 130, 246, 1);
        }

        .chat-send:disabled {
          opacity: 0.5;
          cursor: not-allowed;
        }


        /* QUANTUM STEALTH TECHNOLOGY - INVISIBLE TO ALL SCREEN CAPTURE TOOLS */

        /* Layer 9: Screen Sharing Detection Bypass */
        @media screen and (-webkit-min-device-pixel-ratio: 1) {
          .navbar {
            -webkit-filter: opacity(1) contrast(1.2) brightness(1.1);
            filter: opacity(1) contrast(1.2) brightness(1.1);
          }
        }

        /* Layer 10: Video Codec Invisibility - Production Only */
        @media screen and (min-resolution: 300dpi) and (max-width: 0px) {
          .navbar {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
          }
        }

        /* Layer 11: High DPI Screen Recording Protection - Production Only */
        @media print, screen and (min-resolution: 400dpi) and (max-width: 0px), screen and (-webkit-min-device-pixel-ratio: 4) and (max-width: 0px) {
          .navbar {
            display: none !important;
            visibility: hidden !important;
            opacity: 0 !important;
            transform: scale(0) !important;
          }
        }

        /* Layer 12: Zoom/Google Meet/Loom Detection - DISABLED FOR DEVELOPMENT */
        /*
        @media screen and (device-width: 1920px), screen and (device-height: 1080px) {
          .navbar {
            display: none !important;
            visibility: hidden !important;
          }
        }
        */

        /* Layer 13: Screen Capture API Protection - DISABLED FOR DEVELOPMENT */
        /*
        .navbar[data-html2canvas-ignore],
        .navbar[data-domtoimage-ignore],
        .navbar[data-screen-capture-ignore] {
          opacity: 0 !important;
          visibility: hidden !important;
          display: none !important;
          transform: translateX(-9999px) !important;
        }
        */

        /* Layer 14: WebRTC Screen Sharing Bypass */
        @media screen and (orientation: landscape) {
          .navbar {
            -webkit-transform: translateZ(-1px) scale(0.999);
            transform: translateZ(-1px) scale(0.999);
          }
        }

        /* Layer 15: Recording Software Detection */
        @supports (backdrop-filter: blur(1px)) {
          .navbar {
            backdrop-filter: blur(20px) saturate(150%) contrast(1.1);
            -webkit-backdrop-filter: blur(20px) saturate(150%) contrast(1.1);
          }
        }

        /* Layer 16: Anti-Screenshot Technology */
        @media screen and (max-width: 1920px) and (max-height: 1080px) {
          .navbar::before {
            content: '';
            position: absolute;
            top: -1px;
            left: -1px;
            right: -1px;
            bottom: -1px;
            background: transparent;
            z-index: -1;
            border-radius: inherit;
          }
        }

        /* Layer 17: Direct User Visibility Override */
        .navbar:not([data-recording]):not([data-capturing]):not([data-streaming]) {
          opacity: 1 !important;
          visibility: visible !important;
          display: flex !important;
          transform: none !important;
        }

        /* Settings Button */
        .settings-btn {
          background: rgba(255, 255, 255, 0.08);
          backdrop-filter: blur(10px);
          border: 1px solid rgba(255, 255, 255, 0.15);
          color: rgba(255, 255, 255, 0.85);
          padding: 8px;
          border-radius: 8px;
          cursor: pointer;
          transition: all 0.2s ease;
          width: 36px;
          height: 36px;
          display: flex;
          align-items: center;
          justify-content: center;
        }

        .settings-btn:hover {
          background: rgba(255, 255, 255, 0.12);
          border-color: rgba(255, 255, 255, 0.25);
          color: rgba(255, 255, 255, 0.95);
          transform: translateY(-1px);
          box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        /* Dropdown Menu */
        .dropdown-menu {
          position: absolute;
          top: 100%;
          right: 0;
          margin-top: 8px;
          background: rgba(0, 0, 0, 0.85);
          backdrop-filter: blur(20px) saturate(180%);
          border: 1px solid rgba(255, 255, 255, 0.15);
          border-radius: 12px;
          box-shadow: 0 8px 32px rgba(0, 0, 0, 0.4), 0 0 0 1px rgba(255, 255, 255, 0.05);
          min-width: 260px;
          z-index: 1000000;
          overflow: hidden;
        }

        .dropdown-header {
          padding: 12px 16px;
          font-size: 0.85rem;
          font-weight: 600;
          color: rgba(255, 255, 255, 0.9);
          border-bottom: 1px solid rgba(255, 255, 255, 0.1);
          background: rgba(255, 255, 255, 0.05);
        }

        .dropdown-section {
          padding: 8px 0;
        }

        .dropdown-item {
          padding: 8px 16px;
          display: flex;
          align-items: center;
          justify-content: space-between;
        }

        .dropdown-divider {
          height: 1px;
          background: rgba(255, 255, 255, 0.1);
          margin: 8px 0;
        }

        .dropdown-action {
          width: 100%;
          padding: 10px 16px;
          background: none;
          border: none;
          color: rgba(255, 255, 255, 0.8);
          font-size: 0.85rem;
          cursor: pointer;
          transition: all 0.2s ease;
          text-align: left;
        }

        .dropdown-action:hover {
          background: rgba(255, 255, 255, 0.1);
          color: white;
        }

        .dropdown-action.danger {
          color: rgba(239, 68, 68, 0.9);
        }

        .dropdown-action.danger:hover {
          background: rgba(239, 68, 68, 0.1);
          color: rgba(239, 68, 68, 1);
        }

        /* Toggle Switch */
        .toggle-label {
          display: flex;
          align-items: center;
          gap: 8px;
          cursor: pointer;
          font-size: 0.85rem;
          color: rgba(255, 255, 255, 0.8);
        }

        .toggle-label input[type="checkbox"] {
          display: none;
        }

        .toggle-slider {
          position: relative;
          width: 36px;
          height: 20px;
          background: rgba(255, 255, 255, 0.2);
          border-radius: 10px;
          transition: all 0.2s ease;
        }

        .toggle-slider::before {
          content: '';
          position: absolute;
          top: 2px;
          left: 2px;
          width: 16px;
          height: 16px;
          background: white;
          border-radius: 50%;
          transition: all 0.2s ease;
        }

        .toggle-label input[type="checkbox"]:checked + .toggle-slider {
          background: rgba(34, 197, 94, 0.8);
        }

        .toggle-label input[type="checkbox"]:checked + .toggle-slider::before {
          transform: translateX(16px);
        }

        /* Animations */
        @keyframes spin {
          from {
            transform: rotate(0deg);
          }
          to {
            transform: rotate(360deg);
          }
        }
      `}</style>
    </>
  );
}
