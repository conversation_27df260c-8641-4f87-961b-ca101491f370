// Multi-Model AI Orchestration System for Flora AI
// Implements intelligent model routing, context-aware processing, and response optimization

import { openai } from '@ai-sdk/openai';
import { generateText, streamText } from 'ai';

export interface AIModel {
  id: string;
  name: string;
  provider: 'openrouter' | 'openai' | 'anthropic' | 'local';
  endpoint: string;
  capabilities: ModelCapability[];
  performance: ModelPerformance;
  cost: number; // Cost per 1K tokens
}

export interface ModelCapability {
  type: 'reasoning' | 'coding' | 'conversation' | 'vision' | 'analysis' | 'creative';
  strength: number; // 0-1 scale
  latency: 'low' | 'medium' | 'high';
  contextWindow: number;
}

export interface ModelPerformance {
  averageLatency: number; // milliseconds
  successRate: number; // 0-1 scale
  qualityScore: number; // 0-1 scale
  lastUpdated: number;
}

export interface MeetingContext {
  type: 'interview' | 'meeting' | 'presentation' | 'call' | 'exam';
  phase: 'introduction' | 'discussion' | 'questions' | 'conclusion';
  participants: number;
  duration: number; // minutes
  isRecording: boolean;
  isScreenSharing: boolean;
  platform: string;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  topic?: string;
  keywords: string[];
}

export interface QueryAnalysis {
  type: 'technical' | 'behavioral' | 'coding' | 'general' | 'creative';
  complexity: 'simple' | 'medium' | 'complex' | 'expert';
  urgency: 'low' | 'medium' | 'high' | 'critical';
  requiredCapabilities: ModelCapability['type'][];
  estimatedTokens: number;
  context: MeetingContext;
}

export interface AIResponse {
  content: string;
  confidence: number;
  model: string;
  latency: number;
  tokens: number;
  sources?: string[];
  followUpSuggestions?: string[];
  metadata: ResponseMetadata;
}

export interface ResponseMetadata {
  processingTime: number;
  modelUsed: string;
  fallbackUsed: boolean;
  cacheHit: boolean;
  qualityScore: number;
  timestamp: number;
}

export interface OptimizedResponse {
  text: string;
  confidence: number;
  sources: string[];
  followUps: string[];
  timing: ResponseTiming;
}

export interface ResponseTiming {
  optimal: number; // milliseconds to wait before showing
  duration: number; // how long to display
  urgency: 'immediate' | 'quick' | 'normal' | 'delayed';
}

class AdvancedAIOrchestrator {
  private models: Map<string, AIModel> = new Map();
  private responseCache: Map<string, CachedResponse> = new Map();
  private performanceMetrics: Map<string, ModelPerformance> = new Map();
  private activeRequests: Map<string, Promise<AIResponse>> = new Map();

  constructor() {
    this.initializeModels();
    this.startPerformanceMonitoring();
  }

  private initializeModels(): void {
    // Define available AI models with their capabilities
    const models: AIModel[] = [
      {
        id: 'qwen-vl-72b',
        name: 'Qwen VL 72B Instruct',
        provider: 'openrouter',
        endpoint: 'qwen/qwen2.5-vl-72b-instruct:free',
        capabilities: [
          { type: 'vision', strength: 0.95, latency: 'medium', contextWindow: 32000 },
          { type: 'reasoning', strength: 0.9, latency: 'medium', contextWindow: 32000 },
          { type: 'coding', strength: 0.85, latency: 'medium', contextWindow: 32000 }
        ],
        performance: {
          averageLatency: 2500,
          successRate: 0.95,
          qualityScore: 0.9,
          lastUpdated: Date.now()
        },
        cost: 0.0
      },
      {
        id: 'deepseek-r1',
        name: 'DeepSeek R1 Qwen3 8B',
        provider: 'openrouter',
        endpoint: 'deepseek/deepseek-r1-0528-qwen3-8b:free',
        capabilities: [
          { type: 'reasoning', strength: 0.9, latency: 'low', contextWindow: 8000 },
          { type: 'conversation', strength: 0.85, latency: 'low', contextWindow: 8000 },
          { type: 'analysis', strength: 0.8, latency: 'low', contextWindow: 8000 }
        ],
        performance: {
          averageLatency: 1200,
          successRate: 0.92,
          qualityScore: 0.85,
          lastUpdated: Date.now()
        },
        cost: 0.0
      },
      {
        id: 'llama-90b-vision',
        name: 'Llama 3.2 90B Vision',
        provider: 'openrouter',
        endpoint: 'meta-llama/llama-3.2-90b-vision-instruct:free',
        capabilities: [
          { type: 'vision', strength: 0.9, latency: 'high', contextWindow: 128000 },
          { type: 'reasoning', strength: 0.88, latency: 'high', contextWindow: 128000 },
          { type: 'conversation', strength: 0.9, latency: 'high', contextWindow: 128000 }
        ],
        performance: {
          averageLatency: 4000,
          successRate: 0.88,
          qualityScore: 0.87,
          lastUpdated: Date.now()
        },
        cost: 0.0
      },
      {
        id: 'qwen-coder-32b',
        name: 'Qwen 2.5 Coder 32B',
        provider: 'openrouter',
        endpoint: 'qwen/qwen2.5-coder-32b-instruct:free',
        capabilities: [
          { type: 'coding', strength: 0.95, latency: 'medium', contextWindow: 32000 },
          { type: 'reasoning', strength: 0.85, latency: 'medium', contextWindow: 32000 },
          { type: 'analysis', strength: 0.8, latency: 'medium', contextWindow: 32000 }
        ],
        performance: {
          averageLatency: 2000,
          successRate: 0.93,
          qualityScore: 0.9,
          lastUpdated: Date.now()
        },
        cost: 0.0
      }
    ];

    models.forEach(model => {
      this.models.set(model.id, model);
      this.performanceMetrics.set(model.id, model.performance);
    });
  }

  private startPerformanceMonitoring(): void {
    // Monitor model performance every 5 minutes
    setInterval(() => {
      this.updatePerformanceMetrics();
    }, 5 * 60 * 1000);
  }

  private updatePerformanceMetrics(): void {
    // Update performance metrics based on recent usage
    for (const [modelId, metrics] of this.performanceMetrics) {
      // In a real implementation, you'd calculate these from actual usage data
      metrics.lastUpdated = Date.now();
    }
  }

  async processIntelligently(
    query: string,
    context: MeetingContext,
    options: ProcessingOptions = {}
  ): Promise<AIResponse> {
    const startTime = Date.now();

    try {
      // 1. Analyze the query to understand requirements
      const analysis = await this.analyzeQuery(query, context);

      // 2. Select optimal model(s) based on analysis
      const selectedModels = this.selectOptimalModels(analysis);

      // 3. Check cache for similar queries
      const cacheKey = this.generateCacheKey(query, context);
      const cachedResponse = this.responseCache.get(cacheKey);
      
      if (cachedResponse && this.isCacheValid(cachedResponse)) {
        return this.formatCachedResponse(cachedResponse, startTime);
      }

      // 4. Process with selected model(s)
      const response = await this.executeWithModels(query, selectedModels, analysis, options);

      // 5. Cache the response
      this.cacheResponse(cacheKey, response);

      // 6. Update performance metrics
      this.updateModelMetrics(response.model, response.latency, true);

      return response;
    } catch (error) {
      console.error('AI processing failed:', error);
      
      // Fallback to simple response
      return this.generateFallbackResponse(query, context, startTime);
    }
  }

  private async analyzeQuery(query: string, context: MeetingContext): Promise<QueryAnalysis> {
    const lowerQuery = query.toLowerCase();
    
    // Determine query type
    let type: QueryAnalysis['type'] = 'general';
    if (/\b(code|implement|algorithm|function|debug|programming)\b/.test(lowerQuery)) {
      type = 'coding';
    } else if (/\b(technical|system|architecture|database|api)\b/.test(lowerQuery)) {
      type = 'technical';
    } else if (/\b(tell me about|describe|experience|situation)\b/.test(lowerQuery)) {
      type = 'behavioral';
    } else if (/\b(create|design|write|generate|make)\b/.test(lowerQuery)) {
      type = 'creative';
    }

    // Determine complexity
    let complexity: QueryAnalysis['complexity'] = 'medium';
    if (query.length < 50) {
      complexity = 'simple';
    } else if (query.length > 200 || /\b(complex|advanced|detailed|comprehensive)\b/.test(lowerQuery)) {
      complexity = 'complex';
    } else if (/\b(expert|professional|enterprise|production)\b/.test(lowerQuery)) {
      complexity = 'expert';
    }

    // Determine required capabilities
    const requiredCapabilities: ModelCapability['type'][] = [];
    if (type === 'coding') requiredCapabilities.push('coding', 'reasoning');
    if (type === 'technical') requiredCapabilities.push('reasoning', 'analysis');
    if (type === 'behavioral') requiredCapabilities.push('conversation', 'reasoning');
    if (type === 'creative') requiredCapabilities.push('creative', 'conversation');
    if (context.isScreenSharing) requiredCapabilities.push('vision');

    // Estimate token usage
    const estimatedTokens = Math.ceil(query.length / 4) + this.estimateResponseTokens(complexity);

    return {
      type,
      complexity,
      urgency: context.urgency,
      requiredCapabilities,
      estimatedTokens,
      context
    };
  }

  private estimateResponseTokens(complexity: QueryAnalysis['complexity']): number {
    const tokenEstimates = {
      simple: 100,
      medium: 300,
      complex: 600,
      expert: 1000
    };
    return tokenEstimates[complexity];
  }

  private selectOptimalModels(analysis: QueryAnalysis): AIModel[] {
    const candidates: Array<{ model: AIModel; score: number }> = [];

    for (const model of this.models.values()) {
      let score = 0;

      // Score based on capability match
      for (const requiredCap of analysis.requiredCapabilities) {
        const modelCap = model.capabilities.find(cap => cap.type === requiredCap);
        if (modelCap) {
          score += modelCap.strength * 10;
        }
      }

      // Score based on performance
      const performance = this.performanceMetrics.get(model.id);
      if (performance) {
        score += performance.successRate * 5;
        score += performance.qualityScore * 5;
        
        // Penalty for high latency in urgent situations
        if (analysis.urgency === 'critical' && performance.averageLatency > 3000) {
          score -= 5;
        }
      }

      // Score based on context window requirements
      const maxContextCap = Math.max(...model.capabilities.map(cap => cap.contextWindow));
      if (maxContextCap >= analysis.estimatedTokens) {
        score += 2;
      } else {
        score -= 10; // Heavy penalty for insufficient context
      }

      candidates.push({ model, score });
    }

    // Sort by score and return top models
    candidates.sort((a, b) => b.score - a.score);
    
    // Return top 2 models for redundancy
    return candidates.slice(0, 2).map(c => c.model);
  }

  private async executeWithModels(
    query: string,
    models: AIModel[],
    analysis: QueryAnalysis,
    options: ProcessingOptions
  ): Promise<AIResponse> {
    const primaryModel = models[0];
    const fallbackModel = models[1];

    try {
      // Try primary model first
      return await this.queryModel(query, primaryModel, analysis, options);
    } catch (error) {
      console.warn(`Primary model ${primaryModel.id} failed, trying fallback:`, error);
      
      if (fallbackModel) {
        try {
          const response = await this.queryModel(query, fallbackModel, analysis, options);
          response.metadata.fallbackUsed = true;
          return response;
        } catch (fallbackError) {
          console.error(`Fallback model ${fallbackModel.id} also failed:`, fallbackError);
        }
      }

      throw error;
    }
  }

  private async queryModel(
    query: string,
    model: AIModel,
    analysis: QueryAnalysis,
    options: ProcessingOptions
  ): Promise<AIResponse> {
    const startTime = Date.now();

    // Prepare the prompt based on context
    const systemPrompt = this.buildSystemPrompt(analysis);
    const userPrompt = this.buildUserPrompt(query, analysis);

    try {
      const result = await generateText({
        model: openai(model.endpoint),
        system: systemPrompt,
        prompt: userPrompt,
        maxTokens: this.calculateMaxTokens(analysis),
        temperature: this.calculateTemperature(analysis),
      });

      const latency = Date.now() - startTime;

      return {
        content: result.text,
        confidence: this.calculateConfidence(result.text, analysis),
        model: model.id,
        latency,
        tokens: result.usage?.totalTokens || 0,
        sources: this.extractSources(result.text),
        followUpSuggestions: this.generateFollowUps(result.text, analysis),
        metadata: {
          processingTime: latency,
          modelUsed: model.id,
          fallbackUsed: false,
          cacheHit: false,
          qualityScore: this.assessResponseQuality(result.text, analysis),
          timestamp: Date.now()
        }
      };
    } catch (error) {
      console.error(`Model ${model.id} query failed:`, error);
      throw error;
    }
  }

  private buildSystemPrompt(analysis: QueryAnalysis): string {
    const basePrompt = "You are Flora AI, an advanced professional assistant designed to help users excel in technical interviews, meetings, and professional interactions.";
    
    let contextPrompt = "";
    
    switch (analysis.type) {
      case 'coding':
        contextPrompt = " Focus on providing clear, efficient code solutions with explanations. Consider best practices, performance, and maintainability.";
        break;
      case 'technical':
        contextPrompt = " Provide detailed technical explanations with practical examples. Focus on accuracy and depth of knowledge.";
        break;
      case 'behavioral':
        contextPrompt = " Help craft thoughtful responses that demonstrate leadership, problem-solving, and interpersonal skills. Use the STAR method when appropriate.";
        break;
      case 'creative':
        contextPrompt = " Provide innovative and creative solutions while maintaining professionalism and practicality.";
        break;
      default:
        contextPrompt = " Provide helpful, accurate, and professional responses.";
    }

    const urgencyPrompt = analysis.urgency === 'critical' || analysis.urgency === 'high' 
      ? " Prioritize conciseness and immediate actionability."
      : " Provide comprehensive and detailed responses.";

    return basePrompt + contextPrompt + urgencyPrompt;
  }

  private buildUserPrompt(query: string, analysis: QueryAnalysis): string {
    let prompt = query;

    // Add context information
    if (analysis.context.type === 'interview') {
      prompt += "\n\nContext: This is during a job interview. Please provide a professional response that demonstrates competence and confidence.";
    } else if (analysis.context.type === 'meeting') {
      prompt += "\n\nContext: This is during a professional meeting. Please provide a collaborative and solution-oriented response.";
    }

    // Add urgency context
    if (analysis.urgency === 'critical') {
      prompt += "\n\nUrgency: This requires an immediate response. Please be concise and direct.";
    }

    return prompt;
  }

  private calculateMaxTokens(analysis: QueryAnalysis): number {
    const baseTokens = {
      simple: 200,
      medium: 500,
      complex: 1000,
      expert: 1500
    };

    let tokens = baseTokens[analysis.complexity];

    // Adjust based on urgency
    if (analysis.urgency === 'critical') {
      tokens = Math.min(tokens, 300);
    } else if (analysis.urgency === 'low') {
      tokens = Math.min(tokens * 1.5, 2000);
    }

    return tokens;
  }

  private calculateTemperature(analysis: QueryAnalysis): number {
    // Lower temperature for technical/coding questions, higher for creative
    const temperatureMap = {
      coding: 0.1,
      technical: 0.2,
      behavioral: 0.4,
      creative: 0.7,
      general: 0.3
    };

    return temperatureMap[analysis.type] || 0.3;
  }

  private calculateConfidence(response: string, analysis: QueryAnalysis): number {
    let confidence = 0.7; // Base confidence

    // Boost confidence for longer, more detailed responses
    if (response.length > 200) confidence += 0.1;
    if (response.length > 500) confidence += 0.1;

    // Boost confidence for structured responses
    if (response.includes('\n') || response.includes('•') || response.includes('-')) {
      confidence += 0.1;
    }

    // Reduce confidence for very short responses to complex queries
    if (analysis.complexity === 'complex' && response.length < 100) {
      confidence -= 0.2;
    }

    return Math.min(Math.max(confidence, 0), 1);
  }

  private extractSources(response: string): string[] {
    // Extract potential sources or references from the response
    const sources: string[] = [];
    
    // Look for URLs
    const urlRegex = /https?:\/\/[^\s]+/g;
    const urls = response.match(urlRegex);
    if (urls) {
      sources.push(...urls);
    }

    // Look for documentation references
    const docRegex = /\b(documentation|docs|manual|guide|reference)\b/gi;
    if (docRegex.test(response)) {
      sources.push('Official Documentation');
    }

    return sources;
  }

  private generateFollowUps(response: string, analysis: QueryAnalysis): string[] {
    const followUps: string[] = [];

    // Generate context-appropriate follow-up questions
    switch (analysis.type) {
      case 'coding':
        followUps.push(
          "Would you like me to explain the time complexity?",
          "Should I show alternative implementations?",
          "Do you need help with testing this code?"
        );
        break;
      case 'technical':
        followUps.push(
          "Would you like more details on implementation?",
          "Should I explain the trade-offs?",
          "Do you need examples of this in practice?"
        );
        break;
      case 'behavioral':
        followUps.push(
          "Would you like help preparing for follow-up questions?",
          "Should I suggest ways to strengthen this response?",
          "Do you need examples of similar situations?"
        );
        break;
    }

    return followUps.slice(0, 3); // Limit to 3 follow-ups
  }

  private assessResponseQuality(response: string, analysis: QueryAnalysis): number {
    let quality = 0.5; // Base quality

    // Check response length appropriateness
    const expectedLength = this.getExpectedResponseLength(analysis);
    const lengthRatio = response.length / expectedLength;
    
    if (lengthRatio >= 0.5 && lengthRatio <= 2) {
      quality += 0.2;
    }

    // Check for structure and formatting
    if (response.includes('\n') || response.match(/\d+\./)) {
      quality += 0.1;
    }

    // Check for specific keywords based on type
    const relevantKeywords = this.getRelevantKeywords(analysis.type);
    const keywordMatches = relevantKeywords.filter(keyword => 
      response.toLowerCase().includes(keyword.toLowerCase())
    ).length;
    
    quality += Math.min(keywordMatches * 0.05, 0.2);

    return Math.min(Math.max(quality, 0), 1);
  }

  private getExpectedResponseLength(analysis: QueryAnalysis): number {
    const baseLengths = {
      simple: 150,
      medium: 400,
      complex: 800,
      expert: 1200
    };
    return baseLengths[analysis.complexity];
  }

  private getRelevantKeywords(type: QueryAnalysis['type']): string[] {
    const keywordMap = {
      coding: ['function', 'algorithm', 'implementation', 'solution', 'code', 'method'],
      technical: ['system', 'architecture', 'design', 'performance', 'scalability', 'technology'],
      behavioral: ['experience', 'situation', 'challenge', 'team', 'leadership', 'result'],
      creative: ['innovative', 'creative', 'solution', 'approach', 'idea', 'design'],
      general: ['solution', 'approach', 'method', 'way', 'process', 'strategy']
    };
    return keywordMap[type] || keywordMap.general;
  }

  private generateCacheKey(query: string, context: MeetingContext): string {
    // Create a cache key based on query and relevant context
    const contextKey = `${context.type}_${context.phase}_${context.urgency}`;
    const queryHash = this.simpleHash(query.toLowerCase().trim());
    return `${contextKey}_${queryHash}`;
  }

  private simpleHash(str: string): string {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
      const char = str.charCodeAt(i);
      hash = ((hash << 5) - hash) + char;
      hash = hash & hash; // Convert to 32-bit integer
    }
    return hash.toString(36);
  }

  private isCacheValid(cached: CachedResponse): boolean {
    const maxAge = 5 * 60 * 1000; // 5 minutes
    return Date.now() - cached.timestamp < maxAge;
  }

  private formatCachedResponse(cached: CachedResponse, startTime: number): AIResponse {
    return {
      ...cached.response,
      latency: Date.now() - startTime,
      metadata: {
        ...cached.response.metadata,
        cacheHit: true,
        timestamp: Date.now()
      }
    };
  }

  private cacheResponse(key: string, response: AIResponse): void {
    this.responseCache.set(key, {
      response,
      timestamp: Date.now()
    });

    // Clean old cache entries
    if (this.responseCache.size > 1000) {
      const oldestKey = this.responseCache.keys().next().value;
      this.responseCache.delete(oldestKey);
    }
  }

  private updateModelMetrics(modelId: string, latency: number, success: boolean): void {
    const metrics = this.performanceMetrics.get(modelId);
    if (metrics) {
      // Update running averages
      metrics.averageLatency = (metrics.averageLatency * 0.9) + (latency * 0.1);
      metrics.successRate = (metrics.successRate * 0.95) + (success ? 0.05 : 0);
      metrics.lastUpdated = Date.now();
    }
  }

  private generateFallbackResponse(query: string, context: MeetingContext, startTime: number): AIResponse {
    const fallbackContent = "I apologize, but I'm experiencing technical difficulties. Please rephrase your question, and I'll do my best to assist you.";
    
    return {
      content: fallbackContent,
      confidence: 0.3,
      model: 'fallback',
      latency: Date.now() - startTime,
      tokens: 0,
      sources: [],
      followUpSuggestions: ["Could you rephrase your question?", "Would you like to try a different approach?"],
      metadata: {
        processingTime: Date.now() - startTime,
        modelUsed: 'fallback',
        fallbackUsed: true,
        cacheHit: false,
        qualityScore: 0.3,
        timestamp: Date.now()
      }
    };
  }
}

interface CachedResponse {
  response: AIResponse;
  timestamp: number;
}

interface ProcessingOptions {
  maxLatency?: number;
  preferredModel?: string;
  fallbackEnabled?: boolean;
  cacheEnabled?: boolean;
}

// Export singleton instance
export const aiOrchestrator = new AdvancedAIOrchestrator();
