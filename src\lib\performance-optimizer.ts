// Performance Optimization System for Flora AI
// Implements streaming responses, adaptive quality management, and resource optimization

export interface PerformanceMetrics {
  responseTime: number;
  throughput: number;
  memoryUsage: number;
  cpuUsage: number;
  networkLatency: number;
  errorRate: number;
  cacheHitRate: number;
  timestamp: number;
}

export interface OptimizationSettings {
  enableStreaming: boolean;
  adaptiveQuality: boolean;
  resourceThrottling: boolean;
  cacheOptimization: boolean;
  compressionLevel: 'none' | 'low' | 'medium' | 'high';
  priorityMode: 'latency' | 'throughput' | 'balanced' | 'quality';
}

export interface ResourceLimits {
  maxMemoryMB: number;
  maxCPUPercent: number;
  maxConcurrentRequests: number;
  maxResponseTime: number;
  maxCacheSize: number;
}

export interface StreamingConfig {
  chunkSize: number;
  bufferSize: number;
  flushInterval: number;
  compressionEnabled: boolean;
  priorityQueue: boolean;
}

class AdvancedPerformanceOptimizer {
  private metrics: PerformanceMetrics[] = [];
  private settings: OptimizationSettings;
  private limits: ResourceLimits;
  private streamingConfig: StreamingConfig;
  private performanceObserver: PerformanceObserver | null = null;
  private resourceMonitor: NodeJS.Timeout | null = null;
  private requestQueue: Map<string, any> = new Map();
  private responseCache: Map<string, any> = new Map();
  private isOptimizing = false;

  constructor() {
    this.settings = {
      enableStreaming: true,
      adaptiveQuality: true,
      resourceThrottling: true,
      cacheOptimization: true,
      compressionLevel: 'medium',
      priorityMode: 'balanced'
    };

    this.limits = {
      maxMemoryMB: 512,
      maxCPUPercent: 80,
      maxConcurrentRequests: 10,
      maxResponseTime: 5000,
      maxCacheSize: 100
    };

    this.streamingConfig = {
      chunkSize: 1024,
      bufferSize: 8192,
      flushInterval: 100,
      compressionEnabled: true,
      priorityQueue: true
    };
  }

  async initialize(): Promise<void> {
    try {
      // Initialize performance monitoring
      this.initializePerformanceMonitoring();
      
      // Start resource monitoring
      this.startResourceMonitoring();
      
      // Initialize streaming optimizations
      this.initializeStreamingOptimizations();
      
      // Set up adaptive quality management
      this.setupAdaptiveQuality();
      
      // Initialize caching system
      this.initializeCaching();

      this.isOptimizing = true;
      console.log('Performance optimizer initialized');
    } catch (error) {
      console.error('Failed to initialize performance optimizer:', error);
      throw error;
    }
  }

  private initializePerformanceMonitoring(): void {
    if (typeof window !== 'undefined' && 'PerformanceObserver' in window) {
      this.performanceObserver = new PerformanceObserver((list) => {
        const entries = list.getEntries();
        entries.forEach(entry => {
          this.processPerformanceEntry(entry);
        });
      });

      // Observe different types of performance entries
      try {
        this.performanceObserver.observe({ entryTypes: ['measure', 'navigation', 'resource'] });
      } catch (error) {
        console.warn('Some performance entry types not supported:', error);
      }
    }
  }

  private processPerformanceEntry(entry: PerformanceEntry): void {
    const metric: PerformanceMetrics = {
      responseTime: entry.duration,
      throughput: 0, // Will be calculated separately
      memoryUsage: this.getMemoryUsage(),
      cpuUsage: 0, // Estimated from performance
      networkLatency: entry.name.includes('network') ? entry.duration : 0,
      errorRate: 0, // Tracked separately
      cacheHitRate: this.calculateCacheHitRate(),
      timestamp: Date.now()
    };

    this.addMetric(metric);
  }

  private getMemoryUsage(): number {
    if (typeof window !== 'undefined' && 'performance' in window && 'memory' in performance) {
      const memory = (performance as any).memory;
      return memory.usedJSHeapSize / (1024 * 1024); // Convert to MB
    }
    return 0;
  }

  private calculateCacheHitRate(): number {
    if (this.responseCache.size === 0) return 0;
    
    // This would be calculated based on actual cache hits/misses
    // For now, return a placeholder
    return 0.8;
  }

  private addMetric(metric: PerformanceMetrics): void {
    this.metrics.push(metric);
    
    // Keep only last 1000 metrics
    if (this.metrics.length > 1000) {
      this.metrics = this.metrics.slice(-1000);
    }

    // Trigger optimization if needed
    this.checkOptimizationTriggers(metric);
  }

  private checkOptimizationTriggers(metric: PerformanceMetrics): void {
    // Check if optimization is needed based on metrics
    if (metric.responseTime > this.limits.maxResponseTime) {
      this.optimizeForLatency();
    }

    if (metric.memoryUsage > this.limits.maxMemoryMB) {
      this.optimizeMemoryUsage();
    }

    if (metric.errorRate > 0.05) { // 5% error rate
      this.optimizeForReliability();
    }
  }

  private startResourceMonitoring(): void {
    this.resourceMonitor = setInterval(() => {
      this.monitorResources();
    }, 1000); // Monitor every second
  }

  private monitorResources(): void {
    const currentMetric: PerformanceMetrics = {
      responseTime: this.getAverageResponseTime(),
      throughput: this.calculateThroughput(),
      memoryUsage: this.getMemoryUsage(),
      cpuUsage: this.estimateCPUUsage(),
      networkLatency: this.getAverageNetworkLatency(),
      errorRate: this.calculateErrorRate(),
      cacheHitRate: this.calculateCacheHitRate(),
      timestamp: Date.now()
    };

    this.addMetric(currentMetric);
  }

  private getAverageResponseTime(): number {
    if (this.metrics.length === 0) return 0;
    const recent = this.metrics.slice(-10);
    return recent.reduce((sum, m) => sum + m.responseTime, 0) / recent.length;
  }

  private calculateThroughput(): number {
    // Calculate requests per second
    const oneSecondAgo = Date.now() - 1000;
    const recentMetrics = this.metrics.filter(m => m.timestamp > oneSecondAgo);
    return recentMetrics.length;
  }

  private estimateCPUUsage(): number {
    // Estimate CPU usage based on response times and throughput
    const avgResponseTime = this.getAverageResponseTime();
    const throughput = this.calculateThroughput();
    
    // Simple heuristic: higher response time + higher throughput = higher CPU usage
    return Math.min(100, (avgResponseTime / 100) + (throughput * 5));
  }

  private getAverageNetworkLatency(): number {
    if (this.metrics.length === 0) return 0;
    const recent = this.metrics.slice(-10);
    const networkMetrics = recent.filter(m => m.networkLatency > 0);
    if (networkMetrics.length === 0) return 0;
    return networkMetrics.reduce((sum, m) => sum + m.networkLatency, 0) / networkMetrics.length;
  }

  private calculateErrorRate(): number {
    // This would be calculated based on actual error tracking
    return 0;
  }

  private initializeStreamingOptimizations(): void {
    if (!this.settings.enableStreaming) return;

    // Override fetch for streaming optimization
    const originalFetch = window.fetch;
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const requestId = this.generateRequestId();
      
      try {
        // Add request to queue
        this.addToRequestQueue(requestId, { input, init });
        
        // Apply streaming optimizations
        const optimizedInit = this.optimizeRequest(init);
        
        // Execute request with streaming
        const response = await this.executeStreamingRequest(originalFetch, input, optimizedInit);
        
        // Remove from queue
        this.requestQueue.delete(requestId);
        
        return response;
      } catch (error) {
        this.requestQueue.delete(requestId);
        throw error;
      }
    };
  }

  private generateRequestId(): string {
    return `req_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
  }

  private addToRequestQueue(id: string, request: any): void {
    this.requestQueue.set(id, {
      ...request,
      timestamp: Date.now(),
      priority: this.calculateRequestPriority(request)
    });

    // Enforce concurrent request limit
    if (this.requestQueue.size > this.limits.maxConcurrentRequests) {
      this.throttleRequests();
    }
  }

  private calculateRequestPriority(request: any): number {
    // Calculate priority based on request type and current load
    let priority = 1;
    
    if (request.init?.method === 'POST') priority += 1;
    if (request.input?.toString().includes('/api/')) priority += 2;
    
    return priority;
  }

  private throttleRequests(): void {
    // Remove lowest priority requests if over limit
    const requests = Array.from(this.requestQueue.entries());
    requests.sort((a, b) => a[1].priority - b[1].priority);
    
    while (this.requestQueue.size > this.limits.maxConcurrentRequests) {
      const [id] = requests.shift()!;
      this.requestQueue.delete(id);
    }
  }

  private optimizeRequest(init?: RequestInit): RequestInit {
    const optimized: RequestInit = { ...init };

    // Add compression headers
    if (this.streamingConfig.compressionEnabled) {
      optimized.headers = {
        ...optimized.headers,
        'Accept-Encoding': 'gzip, deflate, br',
        'Content-Encoding': 'gzip'
      };
    }

    // Add streaming headers
    optimized.headers = {
      ...optimized.headers,
      'Cache-Control': 'no-cache',
      'Connection': 'keep-alive'
    };

    return optimized;
  }

  private async executeStreamingRequest(
    originalFetch: typeof fetch,
    input: RequestInfo | URL,
    init?: RequestInit
  ): Promise<Response> {
    const startTime = Date.now();
    
    try {
      const response = await originalFetch(input, init);
      
      // Wrap response for streaming optimization
      return this.wrapStreamingResponse(response, startTime);
    } catch (error) {
      const endTime = Date.now();
      this.recordError(endTime - startTime);
      throw error;
    }
  }

  private wrapStreamingResponse(response: Response, startTime: number): Response {
    if (!response.body) return response;

    const reader = response.body.getReader();
    const stream = new ReadableStream({
      start(controller) {
        const pump = async (): Promise<void> => {
          try {
            const { done, value } = await reader.read();
            
            if (done) {
              controller.close();
              const endTime = Date.now();
              this.recordSuccess(endTime - startTime);
              return;
            }

            // Apply chunk optimization
            const optimizedChunk = this.optimizeChunk(value);
            controller.enqueue(optimizedChunk);
            
            return pump();
          } catch (error) {
            controller.error(error);
            const endTime = Date.now();
            this.recordError(endTime - startTime);
          }
        };

        return pump();
      }
    });

    return new Response(stream, {
      status: response.status,
      statusText: response.statusText,
      headers: response.headers
    });
  }

  private optimizeChunk(chunk: Uint8Array): Uint8Array {
    // Apply chunk-level optimizations
    if (chunk.length > this.streamingConfig.chunkSize) {
      // Split large chunks
      return chunk.slice(0, this.streamingConfig.chunkSize);
    }
    
    return chunk;
  }

  private recordSuccess(duration: number): void {
    // Record successful request metrics
    const metric: PerformanceMetrics = {
      responseTime: duration,
      throughput: 1,
      memoryUsage: this.getMemoryUsage(),
      cpuUsage: this.estimateCPUUsage(),
      networkLatency: duration,
      errorRate: 0,
      cacheHitRate: this.calculateCacheHitRate(),
      timestamp: Date.now()
    };

    this.addMetric(metric);
  }

  private recordError(duration: number): void {
    // Record error metrics
    const metric: PerformanceMetrics = {
      responseTime: duration,
      throughput: 0,
      memoryUsage: this.getMemoryUsage(),
      cpuUsage: this.estimateCPUUsage(),
      networkLatency: duration,
      errorRate: 1,
      cacheHitRate: this.calculateCacheHitRate(),
      timestamp: Date.now()
    };

    this.addMetric(metric);
  }

  private setupAdaptiveQuality(): void {
    if (!this.settings.adaptiveQuality) return;

    // Monitor performance and adjust quality settings
    setInterval(() => {
      this.adjustQualitySettings();
    }, 5000); // Adjust every 5 seconds
  }

  private adjustQualitySettings(): void {
    const recentMetrics = this.metrics.slice(-10);
    if (recentMetrics.length === 0) return;

    const avgResponseTime = recentMetrics.reduce((sum, m) => sum + m.responseTime, 0) / recentMetrics.length;
    const avgMemoryUsage = recentMetrics.reduce((sum, m) => sum + m.memoryUsage, 0) / recentMetrics.length;

    // Adjust based on performance
    if (avgResponseTime > this.limits.maxResponseTime) {
      this.reduceQuality();
    } else if (avgResponseTime < this.limits.maxResponseTime * 0.5 && avgMemoryUsage < this.limits.maxMemoryMB * 0.7) {
      this.increaseQuality();
    }
  }

  private reduceQuality(): void {
    // Reduce quality to improve performance
    if (this.settings.compressionLevel !== 'high') {
      const levels = ['none', 'low', 'medium', 'high'];
      const currentIndex = levels.indexOf(this.settings.compressionLevel);
      this.settings.compressionLevel = levels[Math.min(currentIndex + 1, levels.length - 1)] as any;
    }

    this.streamingConfig.chunkSize = Math.max(512, this.streamingConfig.chunkSize - 256);
  }

  private increaseQuality(): void {
    // Increase quality when performance allows
    if (this.settings.compressionLevel !== 'none') {
      const levels = ['none', 'low', 'medium', 'high'];
      const currentIndex = levels.indexOf(this.settings.compressionLevel);
      this.settings.compressionLevel = levels[Math.max(currentIndex - 1, 0)] as any;
    }

    this.streamingConfig.chunkSize = Math.min(2048, this.streamingConfig.chunkSize + 256);
  }

  private initializeCaching(): void {
    if (!this.settings.cacheOptimization) return;

    // Set up intelligent caching
    this.setupResponseCaching();
    this.setupCacheEviction();
  }

  private setupResponseCaching(): void {
    // Cache responses based on patterns
    const originalFetch = window.fetch;
    
    window.fetch = async (input: RequestInfo | URL, init?: RequestInit) => {
      const cacheKey = this.generateCacheKey(input, init);
      
      // Check cache first
      if (this.responseCache.has(cacheKey)) {
        const cached = this.responseCache.get(cacheKey);
        if (this.isCacheValid(cached)) {
          return cached.response.clone();
        } else {
          this.responseCache.delete(cacheKey);
        }
      }

      // Execute request
      const response = await originalFetch(input, init);
      
      // Cache if appropriate
      if (this.shouldCache(input, init, response)) {
        this.responseCache.set(cacheKey, {
          response: response.clone(),
          timestamp: Date.now(),
          ttl: this.calculateTTL(input, init)
        });
      }

      return response;
    };
  }

  private generateCacheKey(input: RequestInfo | URL, init?: RequestInit): string {
    const url = typeof input === 'string' ? input : input.toString();
    const method = init?.method || 'GET';
    const body = init?.body ? JSON.stringify(init.body) : '';
    
    return `${method}:${url}:${body}`;
  }

  private isCacheValid(cached: any): boolean {
    return Date.now() - cached.timestamp < cached.ttl;
  }

  private shouldCache(input: RequestInfo | URL, init?: RequestInit, response: Response): boolean {
    // Only cache GET requests with successful responses
    const method = init?.method || 'GET';
    return method === 'GET' && response.ok && response.status < 300;
  }

  private calculateTTL(input: RequestInfo | URL, init?: RequestInit): number {
    // Calculate time-to-live based on request type
    const url = typeof input === 'string' ? input : input.toString();
    
    if (url.includes('/api/static/')) return 3600000; // 1 hour
    if (url.includes('/api/')) return 300000; // 5 minutes
    
    return 60000; // 1 minute default
  }

  private setupCacheEviction(): void {
    // Periodically clean up cache
    setInterval(() => {
      this.evictExpiredCache();
    }, 60000); // Clean every minute
  }

  private evictExpiredCache(): void {
    const now = Date.now();
    
    for (const [key, cached] of this.responseCache.entries()) {
      if (now - cached.timestamp > cached.ttl) {
        this.responseCache.delete(key);
      }
    }

    // Enforce cache size limit
    if (this.responseCache.size > this.limits.maxCacheSize) {
      const entries = Array.from(this.responseCache.entries());
      entries.sort((a, b) => a[1].timestamp - b[1].timestamp);
      
      while (this.responseCache.size > this.limits.maxCacheSize) {
        const [key] = entries.shift()!;
        this.responseCache.delete(key);
      }
    }
  }

  // Optimization strategies
  private optimizeForLatency(): void {
    this.settings.priorityMode = 'latency';
    this.streamingConfig.chunkSize = 512;
    this.streamingConfig.flushInterval = 50;
  }

  private optimizeForThroughput(): void {
    this.settings.priorityMode = 'throughput';
    this.streamingConfig.chunkSize = 2048;
    this.streamingConfig.flushInterval = 200;
  }

  private optimizeMemoryUsage(): void {
    // Clear caches
    this.responseCache.clear();
    
    // Reduce buffer sizes
    this.streamingConfig.bufferSize = Math.max(1024, this.streamingConfig.bufferSize / 2);
    
    // Force garbage collection if available
    if (typeof window !== 'undefined' && (window as any).gc) {
      (window as any).gc();
    }
  }

  private optimizeForReliability(): void {
    // Reduce concurrent requests
    this.limits.maxConcurrentRequests = Math.max(1, this.limits.maxConcurrentRequests - 1);
    
    // Increase timeouts
    this.limits.maxResponseTime *= 1.5;
  }

  // Public API methods
  getPerformanceMetrics(): PerformanceMetrics[] {
    return [...this.metrics];
  }

  getCurrentMetrics(): PerformanceMetrics | null {
    return this.metrics.length > 0 ? this.metrics[this.metrics.length - 1] : null;
  }

  getOptimizationSettings(): OptimizationSettings {
    return { ...this.settings };
  }

  updateSettings(settings: Partial<OptimizationSettings>): void {
    this.settings = { ...this.settings, ...settings };
  }

  updateLimits(limits: Partial<ResourceLimits>): void {
    this.limits = { ...this.limits, ...limits };
  }

  clearCache(): void {
    this.responseCache.clear();
  }

  getPerformanceReport(): any {
    const recent = this.metrics.slice(-100);
    
    return {
      averageResponseTime: recent.reduce((sum, m) => sum + m.responseTime, 0) / recent.length,
      averageThroughput: recent.reduce((sum, m) => sum + m.throughput, 0) / recent.length,
      averageMemoryUsage: recent.reduce((sum, m) => sum + m.memoryUsage, 0) / recent.length,
      cacheHitRate: this.calculateCacheHitRate(),
      errorRate: recent.reduce((sum, m) => sum + m.errorRate, 0) / recent.length,
      optimizationLevel: this.settings.priorityMode,
      cacheSize: this.responseCache.size,
      activeRequests: this.requestQueue.size
    };
  }

  destroy(): void {
    if (this.performanceObserver) {
      this.performanceObserver.disconnect();
    }
    
    if (this.resourceMonitor) {
      clearInterval(this.resourceMonitor);
    }
    
    this.responseCache.clear();
    this.requestQueue.clear();
    this.metrics = [];
    this.isOptimizing = false;
  }
}

// Export singleton instance
export const performanceOptimizer = new AdvancedPerformanceOptimizer();
