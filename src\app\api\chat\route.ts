// Demo response generator for when API key is not configured
function generateDemoResponse(problem: string): string {
  const lowerProblem = problem.toLowerCase();

  if (lowerProblem.includes('react') || lowerProblem.includes('component')) {
    return `🌸 Flora AI Demo Response:

Here's a React solution for your problem:

\`\`\`jsx
import React, { useState, useEffect } from 'react';

const SolutionComponent = () => {
  const [data, setData] = useState(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    // Fetch data or perform initialization
    const fetchData = async () => {
      try {
        setLoading(true);
        // Your logic here
        const result = await processData();
        setData(result);
      } catch (error) {
        console.error('Error:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, []);

  if (loading) return <div>Loading...</div>;

  return (
    <div className="solution-container">
      <h2>Solution</h2>
      <pre>{JSON.stringify(data, null, 2)}</pre>
    </div>
  );
};

export default SolutionComponent;
\`\`\`

This demonstrates modern React patterns with hooks, async operations, and proper error handling.

**Note:** This is a demo response. Configure your OpenRouter API key in .env.local for full AI capabilities.`;
  }

  if (lowerProblem.includes('algorithm') || lowerProblem.includes('leetcode')) {
    return `🌸 Flora AI Demo Response:

Here's an algorithmic approach to your problem:

\`\`\`javascript
/**
 * Optimized solution with detailed explanation
 * Time Complexity: O(n log n)
 * Space Complexity: O(1)
 */
function solve(nums) {
  // Edge case handling
  if (!nums || nums.length === 0) return [];

  // Main algorithm
  const result = [];
  const n = nums.length;

  for (let i = 0; i < n; i++) {
    // Process each element efficiently
    const processed = processElement(nums[i], i);
    result.push(processed);
  }

  return result;
}

function processElement(element, index) {
  // Your specific logic here
  return element * 2 + index;
}

// Test cases
console.log(solve([1, 2, 3, 4])); // [2, 5, 8, 11]
console.log(solve([])); // []
console.log(solve([0])); // [0]
\`\`\`

**Algorithm Explanation:**
1. Handle edge cases first
2. Iterate through input efficiently
3. Apply transformation logic
4. Return optimized result

**Note:** This is a demo response. Configure your OpenRouter API key in .env.local for full AI capabilities.`;
  }

  return `🌸 Flora AI Demo Response:

I understand you're asking about: "${problem}"

Here's a comprehensive solution approach:

**Analysis:**
- Problem type: General inquiry
- Complexity: Medium
- Recommended approach: Step-by-step solution

**Solution:**
\`\`\`javascript
// Generic solution template
function solveProblem(input) {
  // 1. Validate input
  if (!input) {
    throw new Error('Input is required');
  }

  // 2. Process the problem
  const steps = [
    'Analyze requirements',
    'Design solution',
    'Implement efficiently',
    'Test thoroughly',
    'Optimize performance'
  ];

  // 3. Execute solution
  const result = steps.map((step, index) => ({
    step: index + 1,
    action: step,
    status: 'completed'
  }));

  return result;
}

// Example usage
const solution = solveProblem("${problem}");
console.log(solution);
\`\`\`

**Key Points:**
- Always validate inputs
- Break down complex problems
- Use efficient algorithms
- Test edge cases
- Document your solution

**Note:** This is a demo response. Configure your OpenRouter API key in .env.local for full AI capabilities with advanced models like Qwen VL 72B, DeepSeek R1, and more.`;
}

export async function POST(req: Request) {
  try {
    const { problem } = await req.json();

    // Debug: Check API key
    console.log('API Key available:', !!process.env.OPENROUTER_API_KEY);
    console.log('API Key length:', process.env.OPENROUTER_API_KEY?.length || 0);

    // Use the API key directly for now
    const apiKey = process.env.OPENROUTER_API_KEY || '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';

    console.log('Using OpenRouter API with key');

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.SITE_URL || 'http://localhost:3000',
        'X-Title': 'Flora AI Agent'
      },
      body: JSON.stringify({
        model: 'meta-llama/llama-3.2-3b-instruct:free',
        messages: [
          {
            role: 'system',
            content: `You are Flora AI, an advanced problem-solving agent. You are NOT a chatbot or assistant. You are a specialized AI agent that analyzes and solves complex problems.

CORE CAPABILITIES:
• Advanced algorithmic problem solving (LeetCode, competitive programming)
• Data structures and algorithms optimization
• System design and architecture analysis
• Code optimization and performance analysis
• Mathematical and logical problem solving
• Technical interview problem resolution

RESPONSE PROTOCOL:
• Analyze the problem systematically
• Provide step-by-step solution methodology
• Include optimized code implementations
• Show time/space complexity analysis
• Offer multiple solution approaches when applicable
• Focus on teaching core concepts and patterns

OUTPUT FORMAT:
• Direct, technical, and solution-focused
• No conversational elements or pleasantries
• Structured analysis with clear sections
• Code examples with detailed explanations
• Performance metrics and optimization insights

You solve problems with precision and expertise, not conversation.`
          },
          {
            role: 'user',
            content: `PROBLEM TO SOLVE: ${problem}`
          }
        ],
        temperature: 0.3,
        max_tokens: 2000,
        stream: true
      })
    });

    if (!response.ok) {
      console.error(`OpenRouter API error: ${response.status}`);
      // Return demo response as fallback
      return new Response(
        generateDemoResponse(problem),
        {
          headers: {
            'Content-Type': 'text/plain; charset=utf-8',
          }
        }
      );
    }

    // Return the streaming response
    return new Response(response.body, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked'
      }
    });

  } catch (error) {
    console.error('Flora AI Agent Error:', error);

    // Return demo response as fallback even on errors
    try {
      const { problem } = await req.json();
      return new Response(
        generateDemoResponse(problem || 'general question'),
        {
          headers: {
            'Content-Type': 'text/plain; charset=utf-8',
          }
        }
      );
    } catch {
      return new Response(
        generateDemoResponse('general question'),
        {
          headers: {
            'Content-Type': 'text/plain; charset=utf-8',
          }
        }
      );
    }
  }
}
