// Demo response generator for when API key is not configured
function generateDemoResponse(problem: string): string {
  const lowerProblem = problem.toLowerCase();

  if (lowerProblem.includes('react') || lowerProblem.includes('component')) {
    return `## React Component Solution

### Analysis
Problem type: React component development
Complexity: Medium
Approach: Modern React patterns with hooks

### Solution

\`\`\`jsx
import React, { useState, useEffect, useCallback } from 'react';

const OptimizedComponent = ({ initialData, onDataChange }) => {
  const [data, setData] = useState(initialData || null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState(null);

  // Memoized data processing function
  const processData = useCallback(async (input) => {
    try {
      setLoading(true);
      setError(null);

      // Simulate API call or data processing
      const response = await fetch('/api/data', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ input })
      });

      if (!response.ok) throw new Error('Data processing failed');

      const result = await response.json();
      return result;
    } catch (err) {
      setError(err.message);
      throw err;
    } finally {
      setLoading(false);
    }
  }, []);

  useEffect(() => {
    if (initialData) {
      processData(initialData).then(result => {
        setData(result);
        onDataChange?.(result);
      });
    }
  }, [initialData, processData, onDataChange]);

  if (loading) {
    return (
      <div className="flex items-center justify-center p-4">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
        <span className="ml-2">Processing...</span>
      </div>
    );
  }

  if (error) {
    return (
      <div className="bg-red-50 border border-red-200 rounded-lg p-4">
        <h3 className="text-red-800 font-semibold">Error</h3>
        <p className="text-red-600">{error}</p>
      </div>
    );
  }

  return (
    <div className="solution-container bg-white rounded-lg shadow-lg p-6">
      <h2 className="text-2xl font-bold mb-4">Solution Results</h2>
      <div className="bg-gray-50 rounded-lg p-4">
        <pre className="text-sm overflow-x-auto">
          {JSON.stringify(data, null, 2)}
        </pre>
      </div>
    </div>
  );
};

export default OptimizedComponent;
\`\`\`

### Key Features
• **Error Handling**: Comprehensive error states and user feedback
• **Performance**: Memoized callbacks and optimized re-renders
• **Accessibility**: Proper loading states and error messages
• **Modern Patterns**: Hooks, async/await, and functional components

### Complexity Analysis
• **Time Complexity**: O(1) for rendering, O(n) for data processing
• **Space Complexity**: O(n) for data storage
• **Re-render Optimization**: useCallback prevents unnecessary re-renders

**Note:** Demo response - Configure OpenRouter API key for advanced AI capabilities.`;
  }

  if (lowerProblem.includes('algorithm') || lowerProblem.includes('leetcode')) {
    return `## Algorithm Solution

### Problem Analysis
• **Type**: Algorithmic optimization problem
• **Difficulty**: Medium to Hard
• **Pattern**: Array processing with optimization
• **Approach**: Multiple solution strategies

### Solution 1: Optimized Approach

\`\`\`javascript
/**
 * Optimized solution with detailed explanation
 * Time Complexity: O(n log n)
 * Space Complexity: O(1) auxiliary space
 */
function optimizedSolve(nums) {
  // Input validation
  if (!nums || nums.length === 0) return [];
  if (nums.length === 1) return [nums[0]];

  const n = nums.length;
  const result = new Array(n);

  // Two-pointer technique for optimization
  let left = 0, right = n - 1;
  let index = 0;

  while (left <= right) {
    if (nums[left] > nums[right]) {
      result[index++] = processElement(nums[left], left);
      left++;
    } else {
      result[index++] = processElement(nums[right], right);
      right--;
    }
  }

  return result;
}

function processElement(element, index) {
  // Optimized processing logic
  return element * 2 + index;
}
\`\`\`

### Solution 2: Divide and Conquer

\`\`\`javascript
/**
 * Divide and conquer approach
 * Time Complexity: O(n log n)
 * Space Complexity: O(log n) for recursion stack
 */
function divideAndConquerSolve(nums, start = 0, end = nums.length - 1) {
  if (start > end) return [];
  if (start === end) return [processElement(nums[start], start)];

  const mid = Math.floor((start + end) / 2);
  const left = divideAndConquerSolve(nums, start, mid);
  const right = divideAndConquerSolve(nums, mid + 1, end);

  return merge(left, right);
}

function merge(left, right) {
  const result = [];
  let i = 0, j = 0;

  while (i < left.length && j < right.length) {
    if (left[i] <= right[j]) {
      result.push(left[i++]);
    } else {
      result.push(right[j++]);
    }
  }

  return result.concat(left.slice(i)).concat(right.slice(j));
}
\`\`\`

### Test Cases & Validation

\`\`\`javascript
// Comprehensive test suite
const testCases = [
  { input: [1, 2, 3, 4], expected: [2, 5, 8, 11] },
  { input: [], expected: [] },
  { input: [0], expected: [0] },
  { input: [5, 1, 9, 3], expected: [10, 3, 18, 9] },
  { input: [-1, -2, -3], expected: [-2, -3, -4] }
];

testCases.forEach((test, index) => {
  const result = optimizedSolve(test.input);
  const passed = JSON.stringify(result) === JSON.stringify(test.expected);
  console.log(\`Test \${index + 1}: \${passed ? 'PASS' : 'FAIL'}\`);
  if (!passed) {
    console.log(\`  Expected: \${test.expected}\`);
    console.log(\`  Got: \${result}\`);
  }
});
\`\`\`

### Complexity Analysis
| Approach | Time | Space | Best For |
|----------|------|-------|----------|
| Optimized | O(n log n) | O(1) | Memory-constrained |
| Divide & Conquer | O(n log n) | O(log n) | Parallel processing |
| Brute Force | O(n²) | O(1) | Small inputs |

### Key Optimizations
• **Two-pointer technique** reduces comparisons
• **In-place processing** minimizes memory usage
• **Early termination** for edge cases
• **Batch processing** for large datasets

**Note:** Demo response - Configure OpenRouter API key for advanced AI capabilities.`;
  }

  return `## Problem Analysis: "${problem}"

### Solution Framework

#### 1. Problem Classification
• **Domain**: General problem solving
• **Complexity**: Medium
• **Approach**: Systematic analysis and implementation
• **Pattern**: Multi-step solution methodology

#### 2. Implementation Strategy

\`\`\`javascript
/**
 * Comprehensive problem solver with error handling
 * Implements best practices for robust solutions
 */
class ProblemSolver {
  constructor(config = {}) {
    this.config = {
      validateInput: true,
      enableLogging: false,
      optimizePerformance: true,
      ...config
    };
    this.metrics = {
      startTime: null,
      endTime: null,
      steps: []
    };
  }

  async solve(input) {
    this.metrics.startTime = performance.now();

    try {
      // Phase 1: Input validation
      this.validateInput(input);

      // Phase 2: Problem analysis
      const analysis = this.analyzeRequirements(input);
      this.logStep('Analysis completed', analysis);

      // Phase 3: Solution design
      const design = this.designSolution(analysis);
      this.logStep('Design completed', design);

      // Phase 4: Implementation
      const implementation = await this.implement(design);
      this.logStep('Implementation completed', implementation);

      // Phase 5: Optimization
      const optimized = this.optimize(implementation);
      this.logStep('Optimization completed', optimized);

      this.metrics.endTime = performance.now();
      return this.formatResult(optimized);

    } catch (error) {
      this.handleError(error);
      throw error;
    }
  }

  validateInput(input) {
    if (!input) {
      throw new Error('Input is required');
    }
    if (typeof input !== 'string' && typeof input !== 'object') {
      throw new Error('Invalid input type');
    }
    return true;
  }

  analyzeRequirements(input) {
    return {
      type: typeof input,
      complexity: this.calculateComplexity(input),
      requirements: this.extractRequirements(input),
      constraints: this.identifyConstraints(input)
    };
  }

  designSolution(analysis) {
    const steps = [
      'Data structure selection',
      'Algorithm design',
      'Error handling strategy',
      'Performance optimization',
      'Testing approach'
    ];

    return {
      steps: steps.map((step, index) => ({
        id: index + 1,
        description: step,
        priority: this.calculatePriority(step, analysis),
        estimatedTime: this.estimateTime(step)
      })),
      architecture: this.designArchitecture(analysis)
    };
  }

  async implement(design) {
    const results = [];

    for (const step of design.steps) {
      const result = await this.executeStep(step);
      results.push(result);
    }

    return {
      steps: results,
      performance: this.measurePerformance(),
      quality: this.assessQuality(results)
    };
  }

  optimize(implementation) {
    return {
      ...implementation,
      optimizations: [
        'Memory usage reduced by 30%',
        'Execution time improved by 45%',
        'Code complexity reduced',
        'Error handling enhanced'
      ],
      finalMetrics: this.getFinalMetrics()
    };
  }

  formatResult(optimized) {
    return {
      solution: optimized,
      metadata: {
        executionTime: this.metrics.endTime - this.metrics.startTime,
        steps: this.metrics.steps.length,
        quality: 'Production-ready',
        confidence: 0.95
      }
    };
  }

  // Helper methods
  calculateComplexity(input) { return 'Medium'; }
  extractRequirements(input) { return ['Functionality', 'Performance', 'Reliability']; }
  identifyConstraints(input) { return ['Time', 'Memory', 'Scalability']; }
  calculatePriority(step, analysis) { return Math.floor(Math.random() * 5) + 1; }
  estimateTime(step) { return \`\${Math.floor(Math.random() * 60) + 10} minutes\`; }
  designArchitecture(analysis) { return 'Modular, scalable architecture'; }
  async executeStep(step) { return { ...step, status: 'completed', result: 'Success' }; }
  measurePerformance() { return { cpu: '15%', memory: '120MB', throughput: '1000 ops/sec' }; }
  assessQuality(results) { return 'High'; }
  getFinalMetrics() { return { efficiency: '95%', reliability: '99.9%' }; }
  logStep(message, data) { this.metrics.steps.push({ message, data, timestamp: Date.now() }); }
  handleError(error) { console.error('Solution error:', error); }
}

// Usage example
const solver = new ProblemSolver({
  validateInput: true,
  enableLogging: true,
  optimizePerformance: true
});

const solution = await solver.solve("${problem}");
console.log('Solution:', solution);
\`\`\`

### Key Implementation Features

#### Architecture Benefits
• **Modular Design**: Separated concerns for maintainability
• **Error Handling**: Comprehensive error management
• **Performance Monitoring**: Built-in metrics and optimization
• **Extensibility**: Easy to add new solution strategies

#### Best Practices Applied
• **Input Validation**: Robust input checking
• **Async Operations**: Non-blocking execution
• **Logging**: Detailed execution tracking
• **Optimization**: Performance-focused implementation

#### Complexity Analysis
• **Time Complexity**: O(n) for most operations
• **Space Complexity**: O(1) auxiliary space
• **Scalability**: Handles large inputs efficiently

**Note:** Demo response - Configure OpenRouter API key for advanced AI capabilities with Qwen VL 72B and other cutting-edge models.`;
}

export async function POST(req: Request) {
  try {
    const { problem } = await req.json();

    // Debug: Check API key
    console.log('API Key available:', !!process.env.OPENROUTER_API_KEY);
    console.log('API Key length:', process.env.OPENROUTER_API_KEY?.length || 0);

    // Use the API key directly for now
    const apiKey = process.env.OPENROUTER_API_KEY || '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';

    console.log('Using OpenRouter API with key');

    const response = await fetch('https://openrouter.ai/api/v1/chat/completions', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${apiKey}`,
        'Content-Type': 'application/json',
        'HTTP-Referer': process.env.SITE_URL || 'http://localhost:3000',
        'X-Title': 'Flora AI Agent'
      },
      body: JSON.stringify({
        model: 'qwen/qwen2.5-vl-72b-instruct:free',
        messages: [
          {
            role: 'system',
            content: `You are Flora AI, an advanced problem-solving agent with expertise in complex technical challenges. You deliver precise, structured solutions with professional code formatting.

CORE CAPABILITIES:
• Advanced algorithmic problem solving (LeetCode, competitive programming)
• Data structures and algorithms optimization
• System design and architecture analysis
• Code optimization and performance analysis
• Mathematical and logical problem solving
• Technical interview problem resolution
• Full-stack development solutions
• Database design and optimization

RESPONSE PROTOCOL:
• Analyze the problem systematically with clear structure
• Provide step-by-step solution methodology
• Include optimized, production-ready code implementations
• Show time/space complexity analysis
• Offer multiple solution approaches when applicable
• Focus on teaching core concepts and patterns
• Use proper code formatting with syntax highlighting

OUTPUT FORMAT:
• Direct, technical, and solution-focused
• Well-structured with clear sections and headers
• Code blocks properly formatted with language specification
• Detailed explanations with performance metrics
• Professional documentation style
• Clean, readable code with comments

FORMATTING REQUIREMENTS:
• Use proper markdown formatting
• Code blocks must specify language (```javascript, ```python, etc.)
• Include clear section headers (## Analysis, ## Solution, ## Complexity)
• Use bullet points for key insights
• Provide clean, indented code with meaningful variable names

You deliver expert-level solutions with precision and clarity.`
          },
          {
            role: 'user',
            content: `PROBLEM TO SOLVE: ${problem}`
          }
        ],
        temperature: 0.2,
        max_tokens: 3000,
        stream: true
      })
    });

    if (!response.ok) {
      console.error(`OpenRouter API error: ${response.status}`);
      // Return demo response as fallback
      return new Response(
        generateDemoResponse(problem),
        {
          headers: {
            'Content-Type': 'text/plain; charset=utf-8',
          }
        }
      );
    }

    // Return the streaming response
    return new Response(response.body, {
      headers: {
        'Content-Type': 'text/plain; charset=utf-8',
        'Transfer-Encoding': 'chunked'
      }
    });

  } catch (error) {
    console.error('Flora AI Agent Error:', error);

    // Return demo response as fallback even on errors
    try {
      const { problem } = await req.json();
      return new Response(
        generateDemoResponse(problem || 'general question'),
        {
          headers: {
            'Content-Type': 'text/plain; charset=utf-8',
          }
        }
      );
    } catch {
      return new Response(
        generateDemoResponse('general question'),
        {
          headers: {
            'Content-Type': 'text/plain; charset=utf-8',
          }
        }
      );
    }
  }
}
