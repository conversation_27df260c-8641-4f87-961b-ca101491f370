// Real-time Context Engine for Flora AI
// Implements continuous context analysis, predictive response generation, and meeting state management

import { audioProcessor, Question, AudioAnalysis } from './audio-processor';
import { screenMonitor, ScreenCapture, ContentAnalysis } from './screen-monitor';
import { aiOrchestrator, MeetingContext, AIResponse } from './ai-orchestrator';

export interface ContextFrame {
  id: string;
  timestamp: number;
  audioData: AudioAnalysis | null;
  screenData: ContentAnalysis | null;
  questions: Question[];
  meetingState: MeetingState;
  userActivity: UserActivity;
  confidence: number;
}

export interface MeetingState {
  type: 'interview' | 'meeting' | 'presentation' | 'call' | 'exam' | 'unknown';
  phase: 'starting' | 'introduction' | 'discussion' | 'questions' | 'conclusion' | 'ended';
  participants: Participant[];
  currentSpeaker: string | null;
  duration: number; // milliseconds
  isActive: boolean;
  platform: string;
  isRecording: boolean;
  isScreenSharing: boolean;
  urgencyLevel: 'low' | 'medium' | 'high' | 'critical';
}

export interface Participant {
  id: string;
  name: string;
  role: 'host' | 'interviewer' | 'participant' | 'presenter' | 'candidate';
  isSpeaking: boolean;
  lastSpoke: number;
  speakingTime: number;
}

export interface UserActivity {
  lastInteraction: number;
  interactionType: 'mouse' | 'keyboard' | 'voice' | 'none';
  isTyping: boolean;
  isIdle: boolean;
  attentionLevel: 'focused' | 'distracted' | 'away';
}

export interface PredictedResponse {
  id: string;
  question: string;
  response: string;
  confidence: number;
  preparationTime: number;
  expiresAt: number;
  context: ContextFrame;
}

export interface ContextInsight {
  type: 'opportunity' | 'warning' | 'suggestion' | 'prediction';
  message: string;
  confidence: number;
  actionable: boolean;
  urgency: 'low' | 'medium' | 'high' | 'critical';
  timestamp: number;
}

class RealTimeContextEngine {
  private isActive = false;
  private contextBuffer: ContextFrame[] = [];
  private currentContext: ContextFrame | null = null;
  private meetingState: MeetingState;
  private predictedResponses: Map<string, PredictedResponse> = new Map();
  private contextInsights: ContextInsight[] = [];
  private analysisInterval: NodeJS.Timeout | null = null;
  private predictionInterval: NodeJS.Timeout | null = null;

  constructor() {
    this.meetingState = {
      type: 'unknown',
      phase: 'starting',
      participants: [],
      currentSpeaker: null,
      duration: 0,
      isActive: false,
      platform: 'unknown',
      isRecording: false,
      isScreenSharing: false,
      urgencyLevel: 'low'
    };

    this.initializeEventListeners();
  }

  private initializeEventListeners(): void {
    // Listen for audio analysis events
    if (typeof window !== 'undefined') {
      window.addEventListener('questionsDetected', (event: any) => {
        this.handleQuestionsDetected(event.detail.questions);
      });

      window.addEventListener('screenAnalysis', (event: any) => {
        this.handleScreenAnalysis(event.detail.capture, event.detail.analysis);
      });

      // Listen for user activity
      document.addEventListener('mousemove', () => this.updateUserActivity('mouse'));
      document.addEventListener('keydown', () => this.updateUserActivity('keyboard'));
      document.addEventListener('click', () => this.updateUserActivity('mouse'));
    }
  }

  async startContextMonitoring(): Promise<void> {
    if (this.isActive) {
      console.log('Context monitoring already active');
      return;
    }

    try {
      // Start audio and screen monitoring
      await audioProcessor.startMonitoring();
      await screenMonitor.startMonitoring();

      this.isActive = true;
      this.meetingState.isActive = true;

      // Start continuous context analysis
      this.analysisInterval = setInterval(() => {
        this.analyzeCurrentContext();
      }, 1000); // Analyze every second

      // Start predictive response generation
      this.predictionInterval = setInterval(() => {
        this.generatePredictiveResponses();
      }, 5000); // Generate predictions every 5 seconds

      console.log('Real-time context monitoring started');
    } catch (error) {
      console.error('Failed to start context monitoring:', error);
      throw error;
    }
  }

  stopContextMonitoring(): void {
    this.isActive = false;
    this.meetingState.isActive = false;

    if (this.analysisInterval) {
      clearInterval(this.analysisInterval);
      this.analysisInterval = null;
    }

    if (this.predictionInterval) {
      clearInterval(this.predictionInterval);
      this.predictionInterval = null;
    }

    audioProcessor.stopMonitoring();
    screenMonitor.stopMonitoring();

    console.log('Context monitoring stopped');
  }

  private async analyzeCurrentContext(): Promise<void> {
    if (!this.isActive) return;

    try {
      // Get current audio analysis
      const audioData = audioProcessor.isMonitoring() 
        ? await this.getCurrentAudioAnalysis() 
        : null;

      // Get current screen analysis
      const screenData = screenMonitor.isMonitoring() 
        ? await this.getCurrentScreenAnalysis() 
        : null;

      // Extract questions from both sources
      const questions = this.extractQuestions(audioData, screenData);

      // Update meeting state
      this.updateMeetingState(audioData, screenData);

      // Get user activity
      const userActivity = this.getCurrentUserActivity();

      // Create context frame
      const contextFrame: ContextFrame = {
        id: `context_${Date.now()}`,
        timestamp: Date.now(),
        audioData,
        screenData,
        questions,
        meetingState: { ...this.meetingState },
        userActivity,
        confidence: this.calculateContextConfidence(audioData, screenData)
      };

      // Add to buffer
      this.addContextFrame(contextFrame);

      // Update current context
      this.currentContext = contextFrame;

      // Generate insights
      this.generateContextInsights(contextFrame);

      // Emit context update event
      this.emitContextUpdate(contextFrame);

    } catch (error) {
      console.error('Context analysis failed:', error);
    }
  }

  private async getCurrentAudioAnalysis(): Promise<AudioAnalysis | null> {
    try {
      // This would get the latest audio analysis from the audio processor
      // For now, return a mock analysis
      return {
        transcript: 'Current audio transcript',
        questions: [],
        speakers: [],
        sentiment: 'neutral',
        meetingPhase: 'discussion'
      };
    } catch (error) {
      console.error('Failed to get audio analysis:', error);
      return null;
    }
  }

  private async getCurrentScreenAnalysis(): Promise<ContentAnalysis | null> {
    try {
      const screenText = await screenMonitor.extractTextFromScreen();
      const platform = await screenMonitor.detectMeetingPlatform();
      
      return {
        text: screenText,
        elements: [],
        meetingPlatform: platform.name !== 'unknown' ? platform : null,
        participants: [],
        screenContent: {
          type: 'meeting',
          title: 'Current Meeting',
          context: screenText.substring(0, 200),
          keywords: this.extractKeywords(screenText),
          importance: 'medium'
        },
        confidence: 0.8
      };
    } catch (error) {
      console.error('Failed to get screen analysis:', error);
      return null;
    }
  }

  private extractQuestions(audioData: AudioAnalysis | null, screenData: ContentAnalysis | null): Question[] {
    const questions: Question[] = [];

    // Extract questions from audio
    if (audioData?.questions) {
      questions.push(...audioData.questions);
    }

    // Extract questions from screen text
    if (screenData?.text) {
      const screenQuestions = audioProcessor.detectQuestions(screenData.text);
      questions.push(...screenQuestions);
    }

    // Remove duplicates and sort by timestamp
    const uniqueQuestions = questions.filter((question, index, array) => 
      array.findIndex(q => q.text.toLowerCase() === question.text.toLowerCase()) === index
    );

    return uniqueQuestions.sort((a, b) => b.timestamp - a.timestamp);
  }

  private updateMeetingState(audioData: AudioAnalysis | null, screenData: ContentAnalysis | null): void {
    // Update meeting type
    if (screenData?.meetingPlatform) {
      this.meetingState.platform = screenData.meetingPlatform.name;
      this.meetingState.isRecording = screenData.meetingPlatform.isRecording;
      this.meetingState.isScreenSharing = screenData.meetingPlatform.isScreenSharing;
    }

    // Update meeting phase
    if (audioData?.meetingPhase) {
      this.meetingState.phase = audioData.meetingPhase;
    }

    // Update participants
    if (screenData?.participants) {
      this.updateParticipants(screenData.participants);
    }

    // Update duration
    if (this.meetingState.isActive) {
      this.meetingState.duration = Date.now() - (this.meetingState.duration || Date.now());
    }

    // Update urgency level
    this.meetingState.urgencyLevel = this.calculateUrgencyLevel(audioData, screenData);

    // Detect meeting type
    this.meetingState.type = this.detectMeetingType(audioData, screenData);
  }

  private updateParticipants(screenParticipants: any[]): void {
    // Update participant list based on screen analysis
    for (const screenParticipant of screenParticipants) {
      const existingParticipant = this.meetingState.participants.find(p => p.id === screenParticipant.id);
      
      if (existingParticipant) {
        existingParticipant.isSpeaking = screenParticipant.isSpeaking;
        if (screenParticipant.isSpeaking) {
          existingParticipant.lastSpoke = Date.now();
        }
      } else {
        this.meetingState.participants.push({
          id: screenParticipant.id,
          name: screenParticipant.name,
          role: screenParticipant.role || 'participant',
          isSpeaking: screenParticipant.isSpeaking,
          lastSpoke: screenParticipant.isSpeaking ? Date.now() : 0,
          speakingTime: 0
        });
      }
    }

    // Update current speaker
    const currentSpeaker = this.meetingState.participants.find(p => p.isSpeaking);
    this.meetingState.currentSpeaker = currentSpeaker?.name || null;
  }

  private calculateUrgencyLevel(audioData: AudioAnalysis | null, screenData: ContentAnalysis | null): MeetingState['urgencyLevel'] {
    let urgencyScore = 0;

    // Check for urgent keywords in audio
    if (audioData?.transcript) {
      const urgentKeywords = ['urgent', 'immediately', 'asap', 'critical', 'emergency', 'deadline'];
      const urgentMatches = urgentKeywords.filter(keyword => 
        audioData.transcript.toLowerCase().includes(keyword)
      ).length;
      urgencyScore += urgentMatches * 2;
    }

    // Check for urgent keywords in screen
    if (screenData?.text) {
      const urgentKeywords = ['urgent', 'immediately', 'asap', 'critical', 'emergency', 'deadline'];
      const urgentMatches = urgentKeywords.filter(keyword => 
        screenData.text.toLowerCase().includes(keyword)
      ).length;
      urgencyScore += urgentMatches * 2;
    }

    // Check for recording/screen sharing (increases urgency)
    if (this.meetingState.isRecording) urgencyScore += 3;
    if (this.meetingState.isScreenSharing) urgencyScore += 2;

    // Check meeting phase
    if (this.meetingState.phase === 'questions') urgencyScore += 2;

    // Determine urgency level
    if (urgencyScore >= 6) return 'critical';
    if (urgencyScore >= 4) return 'high';
    if (urgencyScore >= 2) return 'medium';
    return 'low';
  }

  private detectMeetingType(audioData: AudioAnalysis | null, screenData: ContentAnalysis | null): MeetingState['type'] {
    const combinedText = [
      audioData?.transcript || '',
      screenData?.text || ''
    ].join(' ').toLowerCase();

    // Interview indicators
    if (/\b(interview|candidate|position|job|hire|hiring|recruiter)\b/.test(combinedText)) {
      return 'interview';
    }

    // Presentation indicators
    if (/\b(presentation|slides|present|demo|showing|screen share)\b/.test(combinedText)) {
      return 'presentation';
    }

    // Exam indicators
    if (/\b(exam|test|quiz|assessment|evaluation|coding challenge)\b/.test(combinedText)) {
      return 'exam';
    }

    // Call indicators
    if (/\b(call|phone|dial|ring)\b/.test(combinedText)) {
      return 'call';
    }

    // Default to meeting
    if (this.meetingState.participants.length > 1) {
      return 'meeting';
    }

    return 'unknown';
  }

  private getCurrentUserActivity(): UserActivity {
    const now = Date.now();
    const lastInteraction = this.getLastInteractionTime();
    const timeSinceInteraction = now - lastInteraction;

    return {
      lastInteraction,
      interactionType: this.getLastInteractionType(),
      isTyping: this.detectTyping(),
      isIdle: timeSinceInteraction > 30000, // 30 seconds
      attentionLevel: this.calculateAttentionLevel(timeSinceInteraction)
    };
  }

  private getLastInteractionTime(): number {
    // This would track actual user interactions
    // For now, return current time minus a random offset
    return Date.now() - Math.random() * 10000;
  }

  private getLastInteractionType(): UserActivity['interactionType'] {
    // This would track the actual last interaction type
    return 'mouse';
  }

  private detectTyping(): boolean {
    // This would detect if user is currently typing
    return false;
  }

  private calculateAttentionLevel(timeSinceInteraction: number): UserActivity['attentionLevel'] {
    if (timeSinceInteraction < 5000) return 'focused';
    if (timeSinceInteraction < 30000) return 'distracted';
    return 'away';
  }

  private calculateContextConfidence(audioData: AudioAnalysis | null, screenData: ContentAnalysis | null): number {
    let confidence = 0.5; // Base confidence

    // Boost confidence based on data quality
    if (audioData?.transcript && audioData.transcript.length > 50) {
      confidence += 0.2;
    }

    if (screenData?.text && screenData.text.length > 100) {
      confidence += 0.2;
    }

    // Boost confidence if meeting platform detected
    if (screenData?.meetingPlatform) {
      confidence += 0.1;
    }

    return Math.min(Math.max(confidence, 0), 1);
  }

  private addContextFrame(frame: ContextFrame): void {
    this.contextBuffer.push(frame);

    // Keep buffer size manageable (last 100 frames)
    if (this.contextBuffer.length > 100) {
      this.contextBuffer.shift();
    }
  }

  private generateContextInsights(frame: ContextFrame): void {
    const insights: ContextInsight[] = [];

    // Detect opportunities
    if (frame.questions.length > 0) {
      insights.push({
        type: 'opportunity',
        message: `${frame.questions.length} question(s) detected - ready to assist`,
        confidence: 0.9,
        actionable: true,
        urgency: frame.meetingState.urgencyLevel,
        timestamp: Date.now()
      });
    }

    // Detect warnings
    if (frame.meetingState.isRecording) {
      insights.push({
        type: 'warning',
        message: 'Meeting is being recorded - enhanced stealth mode activated',
        confidence: 1.0,
        actionable: true,
        urgency: 'high',
        timestamp: Date.now()
      });
    }

    // Detect suggestions
    if (frame.userActivity.attentionLevel === 'away') {
      insights.push({
        type: 'suggestion',
        message: 'User appears to be away - consider pausing assistance',
        confidence: 0.7,
        actionable: true,
        urgency: 'low',
        timestamp: Date.now()
      });
    }

    // Add insights to collection
    this.contextInsights.push(...insights);

    // Keep insights manageable (last 50)
    if (this.contextInsights.length > 50) {
      this.contextInsights = this.contextInsights.slice(-50);
    }
  }

  private async generatePredictiveResponses(): Promise<void> {
    if (!this.currentContext) return;

    try {
      // Predict likely questions based on current context
      const likelyQuestions = await this.predictLikelyQuestions(this.currentContext);

      // Generate responses for high-probability questions
      for (const question of likelyQuestions) {
        if (question.probability > 0.7) {
          const response = await this.preGenerateResponse(question.text, this.currentContext);
          
          if (response) {
            this.predictedResponses.set(question.id, {
              id: question.id,
              question: question.text,
              response: response.content,
              confidence: response.confidence,
              preparationTime: Date.now(),
              expiresAt: Date.now() + 5 * 60 * 1000, // 5 minutes
              context: this.currentContext
            });
          }
        }
      }

      // Clean expired predictions
      this.cleanExpiredPredictions();

    } catch (error) {
      console.error('Failed to generate predictive responses:', error);
    }
  }

  private async predictLikelyQuestions(context: ContextFrame): Promise<Array<{id: string, text: string, probability: number}>> {
    // Analyze context to predict likely questions
    const predictions: Array<{id: string, text: string, probability: number}> = [];

    // Based on meeting type
    if (context.meetingState.type === 'interview') {
      predictions.push(
        { id: 'pred_1', text: 'Tell me about yourself', probability: 0.8 },
        { id: 'pred_2', text: 'What are your strengths?', probability: 0.7 },
        { id: 'pred_3', text: 'Why do you want this job?', probability: 0.75 }
      );
    }

    // Based on meeting phase
    if (context.meetingState.phase === 'questions') {
      predictions.push(
        { id: 'pred_4', text: 'Do you have any questions for us?', probability: 0.9 }
      );
    }

    // Based on screen content
    if (context.screenData?.screenContent.type === 'code') {
      predictions.push(
        { id: 'pred_5', text: 'Can you explain this algorithm?', probability: 0.8 },
        { id: 'pred_6', text: 'How would you optimize this code?', probability: 0.7 }
      );
    }

    return predictions;
  }

  private async preGenerateResponse(question: string, context: ContextFrame): Promise<AIResponse | null> {
    try {
      const meetingContext: MeetingContext = {
        type: context.meetingState.type,
        phase: context.meetingState.phase,
        participants: context.meetingState.participants.length,
        duration: context.meetingState.duration,
        isRecording: context.meetingState.isRecording,
        isScreenSharing: context.meetingState.isScreenSharing,
        platform: context.meetingState.platform,
        urgency: context.meetingState.urgencyLevel,
        keywords: context.screenData?.screenContent.keywords || []
      };

      return await aiOrchestrator.processIntelligently(question, meetingContext);
    } catch (error) {
      console.error('Failed to pre-generate response:', error);
      return null;
    }
  }

  private cleanExpiredPredictions(): void {
    const now = Date.now();
    for (const [id, prediction] of this.predictedResponses) {
      if (prediction.expiresAt < now) {
        this.predictedResponses.delete(id);
      }
    }
  }

  private extractKeywords(text: string): string[] {
    const words = text.toLowerCase().split(/\s+/);
    const stopWords = new Set(['the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by']);
    
    const keywords = words
      .filter(word => word.length > 3 && !stopWords.has(word))
      .reduce((acc, word) => {
        acc[word] = (acc[word] || 0) + 1;
        return acc;
      }, {} as Record<string, number>);

    return Object.entries(keywords)
      .sort(([, a], [, b]) => b - a)
      .slice(0, 10)
      .map(([word]) => word);
  }

  private handleQuestionsDetected(questions: Question[]): void {
    // Handle questions detected from audio
    if (this.currentContext) {
      this.currentContext.questions.push(...questions);
    }
  }

  private handleScreenAnalysis(capture: ScreenCapture, analysis: ContentAnalysis): void {
    // Handle screen analysis updates
    if (this.currentContext) {
      this.currentContext.screenData = analysis;
    }
  }

  private updateUserActivity(type: 'mouse' | 'keyboard'): void {
    // Update user activity tracking
    // This would be implemented with actual activity tracking
  }

  private emitContextUpdate(context: ContextFrame): void {
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('contextUpdate', {
        detail: { context }
      });
      window.dispatchEvent(event);
    }
  }

  // Public API methods
  getCurrentContext(): ContextFrame | null {
    return this.currentContext;
  }

  getMeetingState(): MeetingState {
    return { ...this.meetingState };
  }

  getContextInsights(): ContextInsight[] {
    return [...this.contextInsights];
  }

  getPredictedResponse(questionText: string): PredictedResponse | null {
    for (const prediction of this.predictedResponses.values()) {
      if (prediction.question.toLowerCase().includes(questionText.toLowerCase()) ||
          questionText.toLowerCase().includes(prediction.question.toLowerCase())) {
        return prediction;
      }
    }
    return null;
  }

  getContextHistory(limit: number = 10): ContextFrame[] {
    return this.contextBuffer.slice(-limit);
  }

  isMonitoring(): boolean {
    return this.isActive;
  }
}

// Export singleton instance
export const contextEngine = new RealTimeContextEngine();
