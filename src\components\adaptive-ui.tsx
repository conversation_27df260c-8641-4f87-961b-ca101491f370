'use client';

// Professional UI Enhancement System for Flora AI
// Implements adaptive interface, proactive assistance, and multi-modal input support

import React, { useState, useEffect, useRef, useCallback } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { contextEngine, ContextFrame, MeetingState, ContextInsight } from '../lib/context-engine';
import { stealthManager, StealthStatus } from '../lib/stealth-manager';
import { aiOrchestrator, AIResponse } from '../lib/ai-orchestrator';

interface AdaptiveUIProps {
  onResponse?: (response: AIResponse) => void;
  onStealthChange?: (status: StealthStatus) => void;
}

interface UIState {
  mode: 'minimal' | 'standard' | 'expanded' | 'hidden';
  position: { x: number; y: number };
  size: { width: number; height: number };
  opacity: number;
  theme: 'dark' | 'glass' | 'stealth';
  urgency: 'low' | 'medium' | 'high' | 'critical';
}

interface ProactiveNotification {
  id: string;
  type: 'opportunity' | 'warning' | 'suggestion' | 'prediction';
  message: string;
  action?: () => void;
  duration: number;
  urgency: 'low' | 'medium' | 'high' | 'critical';
}

interface GestureState {
  isActive: boolean;
  currentGesture: string | null;
  confidence: number;
  lastGesture: number;
}

export const AdaptiveUI: React.FC<AdaptiveUIProps> = ({ onResponse, onStealthChange }) => {
  const [uiState, setUIState] = useState<UIState>({
    mode: 'standard',
    position: { x: window.innerWidth - 420, y: 50 },
    size: { width: 400, height: 600 },
    opacity: 0.95,
    theme: 'glass',
    urgency: 'low'
  });

  const [currentContext, setCurrentContext] = useState<ContextFrame | null>(null);
  const [meetingState, setMeetingState] = useState<MeetingState | null>(null);
  const [stealthStatus, setStealthStatus] = useState<StealthStatus | null>(null);
  const [notifications, setNotifications] = useState<ProactiveNotification[]>([]);
  const [gestureState, setGestureState] = useState<GestureState>({
    isActive: false,
    currentGesture: null,
    confidence: 0,
    lastGesture: 0
  });

  const [isProcessing, setIsProcessing] = useState(false);
  const [currentResponse, setCurrentResponse] = useState<AIResponse | null>(null);
  const [inputValue, setInputValue] = useState('');
  const [showInput, setShowInput] = useState(false);

  const containerRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const gestureRef = useRef<HTMLDivElement>(null);

  // Initialize adaptive UI system
  useEffect(() => {
    const initializeUI = async () => {
      try {
        // Initialize stealth manager
        await stealthManager.initialize();
        
        // Start context monitoring
        await contextEngine.startContextMonitoring();

        // Set up event listeners
        setupEventListeners();

        // Initialize gesture recognition
        initializeGestureRecognition();

        console.log('Adaptive UI system initialized');
      } catch (error) {
        console.error('Failed to initialize adaptive UI:', error);
      }
    };

    initializeUI();

    return () => {
      contextEngine.stopContextMonitoring();
      stealthManager.destroy();
    };
  }, []);

  const setupEventListeners = useCallback(() => {
    // Context updates
    const handleContextUpdate = (event: any) => {
      const context = event.detail.context as ContextFrame;
      setCurrentContext(context);
      setMeetingState(context.meetingState);
      adaptUIToContext(context);
    };

    // Stealth status updates
    const handleStealthUpdate = () => {
      const status = stealthManager.getStealthStatus();
      setStealthStatus(status);
      onStealthChange?.(status);
      adaptUIToStealth(status);
    };

    // Threat detection
    const handleThreatDetected = (event: any) => {
      const threat = event.detail.threat;
      handleThreatResponse(threat);
    };

    // Keyboard shortcuts
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.ctrlKey && event.shiftKey && event.key === 'Enter') {
        event.preventDefault();
        toggleChatInput();
      }
      
      if (event.key === 'Escape') {
        setShowInput(false);
      }
    };

    window.addEventListener('contextUpdate', handleContextUpdate);
    window.addEventListener('threatDetected', handleThreatDetected);
    document.addEventListener('keydown', handleKeyDown);

    // Stealth status polling
    const stealthInterval = setInterval(handleStealthUpdate, 1000);

    return () => {
      window.removeEventListener('contextUpdate', handleContextUpdate);
      window.removeEventListener('threatDetected', handleThreatDetected);
      document.removeEventListener('keydown', handleKeyDown);
      clearInterval(stealthInterval);
    };
  }, [onStealthChange]);

  const adaptUIToContext = useCallback((context: ContextFrame) => {
    setUIState(prevState => {
      const newState = { ...prevState };

      // Adapt to meeting urgency
      newState.urgency = context.meetingState.urgencyLevel;

      // Adapt UI mode based on context
      if (context.meetingState.isRecording || context.meetingState.isScreenSharing) {
        newState.mode = 'minimal';
        newState.opacity = 0.3;
      } else if (context.questions.length > 0) {
        newState.mode = 'expanded';
        newState.opacity = 0.95;
      } else if (context.userActivity.attentionLevel === 'away') {
        newState.mode = 'minimal';
        newState.opacity = 0.5;
      } else {
        newState.mode = 'standard';
        newState.opacity = 0.9;
      }

      // Adapt position based on screen sharing - HORIZONTAL ONLY
      const currentY = newState.position.y; // Preserve current Y position
      if (context.meetingState.isScreenSharing) {
        newState.position = { x: -1000, y: currentY }; // Move off-screen horizontally
      } else {
        newState.position = { x: window.innerWidth - newState.size.width - 50, y: currentY };
      }

      return newState;
    });

    // Generate proactive notifications
    generateProactiveNotifications(context);
  }, []);

  const adaptUIToStealth = useCallback((status: StealthStatus) => {
    setUIState(prevState => ({
      ...prevState,
      theme: status.level === 'paranoid' ? 'stealth' : 'glass',
      opacity: status.confidence * 0.9 + 0.1 // Min 0.1, max 1.0
    }));
  }, []);

  const generateProactiveNotifications = useCallback((context: ContextFrame) => {
    const insights = contextEngine.getContextInsights();
    const newNotifications: ProactiveNotification[] = [];

    insights.forEach(insight => {
      if (insight.actionable && insight.timestamp > Date.now() - 10000) { // Last 10 seconds
        newNotifications.push({
          id: `notification_${insight.timestamp}`,
          type: insight.type,
          message: insight.message,
          action: insight.type === 'opportunity' ? () => handleOpportunityAction(insight) : undefined,
          duration: insight.urgency === 'critical' ? 10000 : 5000,
          urgency: insight.urgency
        });
      }
    });

    setNotifications(prev => [...prev, ...newNotifications]);

    // Auto-remove notifications after duration
    newNotifications.forEach(notification => {
      setTimeout(() => {
        setNotifications(prev => prev.filter(n => n.id !== notification.id));
      }, notification.duration);
    });
  }, []);

  const handleOpportunityAction = useCallback((insight: ContextInsight) => {
    // Handle opportunity actions (e.g., show suggested response)
    if (insight.type === 'opportunity') {
      setShowInput(true);
      inputRef.current?.focus();
    }
  }, []);

  const handleThreatResponse = useCallback(async (threat: any) => {
    if (threat.severity === 'critical') {
      await stealthManager.emergencyHide();
      setUIState(prev => ({ ...prev, mode: 'hidden' }));
    } else if (threat.severity === 'high') {
      setUIState(prev => ({
        ...prev,
        mode: 'minimal',
        opacity: 0.1,
        position: { x: -500, y: prev.position.y } // Keep Y position unchanged
      }));
    }
  }, []);

  const initializeGestureRecognition = useCallback(() => {
    if (!gestureRef.current) return;

    let touchStartTime = 0;
    let touchStartPos = { x: 0, y: 0 };

    const handleTouchStart = (e: TouchEvent) => {
      touchStartTime = Date.now();
      touchStartPos = { x: e.touches[0].clientX, y: e.touches[0].clientY };
    };

    const handleTouchEnd = (e: TouchEvent) => {
      const touchEndTime = Date.now();
      const touchEndPos = { x: e.changedTouches[0].clientX, y: e.changedTouches[0].clientY };
      
      const duration = touchEndTime - touchStartTime;
      const distance = Math.sqrt(
        Math.pow(touchEndPos.x - touchStartPos.x, 2) + 
        Math.pow(touchEndPos.y - touchStartPos.y, 2)
      );

      // Detect gestures
      if (duration < 200 && distance < 10) {
        handleGesture('tap');
      } else if (duration > 500 && distance < 20) {
        handleGesture('long-press');
      } else if (distance > 100) {
        const angle = Math.atan2(touchEndPos.y - touchStartPos.y, touchEndPos.x - touchStartPos.x);
        const degrees = angle * 180 / Math.PI;
        
        if (Math.abs(degrees) < 45) {
          handleGesture('swipe-right');
        } else if (Math.abs(degrees) > 135) {
          handleGesture('swipe-left');
        } else if (degrees > 45 && degrees < 135) {
          handleGesture('swipe-down');
        } else {
          handleGesture('swipe-up');
        }
      }
    };

    gestureRef.current.addEventListener('touchstart', handleTouchStart);
    gestureRef.current.addEventListener('touchend', handleTouchEnd);

    return () => {
      if (gestureRef.current) {
        gestureRef.current.removeEventListener('touchstart', handleTouchStart);
        gestureRef.current.removeEventListener('touchend', handleTouchEnd);
      }
    };
  }, []);

  const handleGesture = useCallback((gesture: string) => {
    setGestureState({
      isActive: true,
      currentGesture: gesture,
      confidence: 0.9,
      lastGesture: Date.now()
    });

    // Handle gesture actions
    switch (gesture) {
      case 'double-tap':
        toggleChatInput();
        break;
      case 'long-press':
        setUIState(prev => ({ ...prev, mode: prev.mode === 'minimal' ? 'expanded' : 'minimal' }));
        break;
      case 'swipe-up':
        setUIState(prev => ({ ...prev, opacity: Math.min(prev.opacity + 0.1, 1) }));
        break;
      case 'swipe-down':
        setUIState(prev => ({ ...prev, opacity: Math.max(prev.opacity - 0.1, 0.1) }));
        break;
    }

    // Clear gesture state after 1 second
    setTimeout(() => {
      setGestureState(prev => ({ ...prev, isActive: false, currentGesture: null }));
    }, 1000);
  }, []);

  const toggleChatInput = useCallback(() => {
    setShowInput(prev => !prev);
    if (!showInput) {
      setTimeout(() => inputRef.current?.focus(), 100);
    }
  }, [showInput]);

  const handleSubmit = useCallback(async (e: React.FormEvent) => {
    e.preventDefault();
    if (!inputValue.trim() || isProcessing) return;

    setIsProcessing(true);
    
    try {
      const context = contextEngine.getCurrentContext();
      const meetingContext = context ? {
        type: context.meetingState.type,
        phase: context.meetingState.phase,
        participants: context.meetingState.participants.length,
        duration: context.meetingState.duration,
        isRecording: context.meetingState.isRecording,
        isScreenSharing: context.meetingState.isScreenSharing,
        platform: context.meetingState.platform,
        urgency: context.meetingState.urgencyLevel,
        keywords: context.screenData?.screenContent.keywords || []
      } : {
        type: 'unknown' as const,
        phase: 'discussion' as const,
        participants: 1,
        duration: 0,
        isRecording: false,
        isScreenSharing: false,
        platform: 'unknown',
        urgency: 'low' as const,
        keywords: []
      };

      const response = await aiOrchestrator.processIntelligently(inputValue, meetingContext);
      
      setCurrentResponse(response);
      onResponse?.(response);
      setInputValue('');
      setShowInput(false);
    } catch (error) {
      console.error('Failed to process query:', error);
    } finally {
      setIsProcessing(false);
    }
  }, [inputValue, isProcessing, onResponse]);

  const getThemeStyles = useCallback(() => {
    const baseStyles = {
      backdropFilter: 'blur(20px)',
      border: '1px solid rgba(255, 255, 255, 0.1)',
      borderRadius: '16px',
      boxShadow: '0 8px 32px rgba(0, 0, 0, 0.3)',
    };

    switch (uiState.theme) {
      case 'glass':
        return {
          ...baseStyles,
          background: 'rgba(0, 0, 0, 0.8)',
          color: 'white',
        };
      case 'stealth':
        return {
          ...baseStyles,
          background: 'rgba(0, 0, 0, 0.95)',
          color: '#00ff00',
          border: '1px solid rgba(0, 255, 0, 0.3)',
        };
      default:
        return {
          ...baseStyles,
          background: 'rgba(20, 20, 20, 0.9)',
          color: 'white',
        };
    }
  }, [uiState.theme]);

  const getUrgencyColor = useCallback(() => {
    switch (uiState.urgency) {
      case 'critical': return '#ff4444';
      case 'high': return '#ff8800';
      case 'medium': return '#ffaa00';
      default: return '#00aa00';
    }
  }, [uiState.urgency]);

  if (uiState.mode === 'hidden') {
    return null;
  }

  return (
    <motion.div
      ref={containerRef}
      className="fixed z-[999999] select-none"
      style={{
        left: uiState.position.x,
        top: uiState.position.y,
        width: uiState.size.width,
        height: uiState.mode === 'minimal' ? 60 : uiState.size.height,
        opacity: uiState.opacity,
        ...getThemeStyles()
      }}
      initial={{ scale: 0.8, opacity: 0 }}
      animate={{ scale: 1, opacity: uiState.opacity }}
      transition={{ duration: 0.3 }}
    >
      {/* Gesture Recognition Area */}
      <div
        ref={gestureRef}
        className="absolute inset-0 touch-none"
        style={{ zIndex: 1 }}
      />

      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-white/10">
        <div className="flex items-center space-x-2">
          <div 
            className="w-3 h-3 rounded-full"
            style={{ backgroundColor: getUrgencyColor() }}
          />
          <span className="text-sm font-medium">Flora AI</span>
          {stealthStatus && (
            <span className="text-xs opacity-60">
              {Math.round(stealthStatus.confidence * 100)}%
            </span>
          )}
        </div>
        
        <div className="flex items-center space-x-2">
          {meetingState?.isRecording && (
            <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse" />
          )}
          {meetingState?.isScreenSharing && (
            <div className="w-2 h-2 bg-yellow-500 rounded-full" />
          )}
        </div>
      </div>

      {/* Main Content */}
      {uiState.mode !== 'minimal' && (
        <div className="p-4 space-y-4 overflow-y-auto" style={{ height: 'calc(100% - 80px)' }}>
          {/* Context Information */}
          {currentContext && (
            <div className="text-xs opacity-80 space-y-1">
              <div>Meeting: {currentContext.meetingState.type} ({currentContext.meetingState.phase})</div>
              <div>Participants: {currentContext.meetingState.participants.length}</div>
              {currentContext.questions.length > 0 && (
                <div className="text-yellow-400">
                  {currentContext.questions.length} question(s) detected
                </div>
              )}
            </div>
          )}

          {/* Proactive Notifications */}
          <AnimatePresence>
            {notifications.map(notification => (
              <motion.div
                key={notification.id}
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                className={`p-3 rounded-lg text-sm ${
                  notification.urgency === 'critical' ? 'bg-red-500/20 border border-red-500/30' :
                  notification.urgency === 'high' ? 'bg-orange-500/20 border border-orange-500/30' :
                  notification.urgency === 'medium' ? 'bg-yellow-500/20 border border-yellow-500/30' :
                  'bg-green-500/20 border border-green-500/30'
                }`}
              >
                <div className="flex items-center justify-between">
                  <span>{notification.message}</span>
                  {notification.action && (
                    <button
                      onClick={notification.action}
                      className="ml-2 px-2 py-1 bg-white/10 rounded text-xs hover:bg-white/20"
                    >
                      Act
                    </button>
                  )}
                </div>
              </motion.div>
            ))}
          </AnimatePresence>

          {/* Current Response */}
          {currentResponse && (
            <div className="bg-white/5 rounded-lg p-3">
              <div className="text-sm mb-2 opacity-80">
                Response ({Math.round(currentResponse.confidence * 100)}% confidence)
              </div>
              <div className="text-sm whitespace-pre-wrap">
                {currentResponse.content}
              </div>
              {currentResponse.followUpSuggestions && currentResponse.followUpSuggestions.length > 0 && (
                <div className="mt-2 space-y-1">
                  <div className="text-xs opacity-60">Follow-up suggestions:</div>
                  {currentResponse.followUpSuggestions.map((suggestion, index) => (
                    <button
                      key={index}
                      onClick={() => setInputValue(suggestion)}
                      className="block text-xs text-left p-1 hover:bg-white/10 rounded w-full"
                    >
                      {suggestion}
                    </button>
                  ))}
                </div>
              )}
            </div>
          )}

          {/* Processing Indicator */}
          {isProcessing && (
            <div className="flex items-center space-x-2 text-sm opacity-80">
              <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
              <span>Processing...</span>
            </div>
          )}
        </div>
      )}

      {/* Chat Input */}
      <AnimatePresence>
        {showInput && (
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: 20 }}
            className="absolute bottom-0 left-0 right-0 p-4 border-t border-white/10"
            style={getThemeStyles()}
          >
            <form onSubmit={handleSubmit} className="flex space-x-2">
              <input
                ref={inputRef}
                type="text"
                value={inputValue}
                onChange={(e) => setInputValue(e.target.value)}
                placeholder="Ask Flora AI..."
                className="flex-1 bg-white/10 border border-white/20 rounded-lg px-3 py-2 text-sm focus:outline-none focus:border-white/40"
                disabled={isProcessing}
              />
              <button
                type="submit"
                disabled={!inputValue.trim() || isProcessing}
                className="px-4 py-2 bg-blue-500 hover:bg-blue-600 disabled:bg-gray-500 rounded-lg text-sm font-medium transition-colors"
              >
                Send
              </button>
            </form>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Quick Action Button */}
      {uiState.mode === 'minimal' && (
        <button
          onClick={toggleChatInput}
          className="absolute inset-0 flex items-center justify-center hover:bg-white/10 transition-colors"
        >
          <span className="text-sm">Flora AI</span>
        </button>
      )}

      {/* Gesture Indicator */}
      {gestureState.isActive && (
        <div className="absolute top-2 right-2 text-xs opacity-60">
          {gestureState.currentGesture}
        </div>
      )}
    </motion.div>
  );
};
