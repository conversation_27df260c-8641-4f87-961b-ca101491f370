// Advanced Audio Processing System for Flora AI
// Implements real-time audio capture, speech-to-text, and question detection

export interface AudioContextService {
  startMonitoring(): Promise<void>;
  stopMonitoring(): void;
  processAudioStream(stream: MediaStream): void;
  extractSpeechToText(audio: AudioBuffer): Promise<string>;
  detectQuestions(text: string): Question[];
  isMonitoring(): boolean;
}

export interface Question {
  id: string;
  text: string;
  timestamp: number;
  confidence: number;
  type: 'technical' | 'behavioral' | 'general' | 'coding';
  urgency: 'low' | 'medium' | 'high' | 'critical';
  context: string;
}

export interface AudioAnalysis {
  transcript: string;
  questions: Question[];
  speakers: Speaker[];
  sentiment: 'positive' | 'neutral' | 'negative';
  meetingPhase: 'introduction' | 'discussion' | 'questions' | 'conclusion';
}

export interface Speaker {
  id: string;
  name?: string;
  confidence: number;
  segments: SpeechSegment[];
}

export interface SpeechSegment {
  start: number;
  end: number;
  text: string;
  confidence: number;
}

class AdvancedAudioProcessor implements AudioContextService {
  private audioContext: AudioContext | null = null;
  private mediaStream: MediaStream | null = null;
  private processor: ScriptProcessorNode | null = null;
  private isActive = false;
  private audioBuffer: Float32Array[] = [];
  private questionPatterns: RegExp[] = [];
  private speechRecognition: any = null;

  constructor() {
    this.initializeQuestionPatterns();
    this.initializeSpeechRecognition();
  }

  private initializeQuestionPatterns(): void {
    // Advanced question detection patterns
    this.questionPatterns = [
      // Technical questions
      /\b(how|what|why|when|where|which|can you|could you|would you|do you|did you|will you|have you|are you|is it|does it|should|explain|describe|tell me about|walk me through)\b.*\?/gi,
      
      // Coding questions
      /\b(implement|code|write|algorithm|function|method|class|debug|optimize|refactor|test)\b.*\?/gi,
      
      // Behavioral questions
      /\b(tell me about a time|describe a situation|give me an example|how do you handle|what would you do|how would you approach)\b/gi,
      
      // System design questions
      /\b(design|architect|scale|build|create|system|database|api|microservice|infrastructure)\b.*\?/gi,
      
      // Problem-solving questions
      /\b(solve|problem|challenge|issue|difficulty|obstacle|solution|approach|strategy)\b.*\?/gi
    ];
  }

  private initializeSpeechRecognition(): void {
    if (typeof window !== 'undefined') {
      const SpeechRecognition = (window as any).SpeechRecognition || (window as any).webkitSpeechRecognition;
      if (SpeechRecognition) {
        this.speechRecognition = new SpeechRecognition();
        this.speechRecognition.continuous = true;
        this.speechRecognition.interimResults = true;
        this.speechRecognition.lang = 'en-US';
      }
    }
  }

  async startMonitoring(): Promise<void> {
    try {
      // Request system audio access (requires special permissions in Electron)
      this.mediaStream = await navigator.mediaDevices.getUserMedia({
        audio: {
          echoCancellation: true,
          noiseSuppression: true,
          autoGainControl: true,
          sampleRate: 44100,
          channelCount: 2
        }
      });

      this.audioContext = new AudioContext({ sampleRate: 44100 });
      const source = this.audioContext.createMediaStreamSource(this.mediaStream);
      
      // Create audio processor for real-time analysis
      this.processor = this.audioContext.createScriptProcessor(4096, 2, 2);
      
      this.processor.onaudioprocess = (event) => {
        this.processAudioData(event.inputBuffer);
      };

      source.connect(this.processor);
      this.processor.connect(this.audioContext.destination);

      // Start speech recognition
      if (this.speechRecognition) {
        this.speechRecognition.onresult = this.handleSpeechResult.bind(this);
        this.speechRecognition.start();
      }

      this.isActive = true;
      console.log('Audio monitoring started successfully');
    } catch (error) {
      console.error('Failed to start audio monitoring:', error);
      throw new Error('Audio monitoring initialization failed');
    }
  }

  stopMonitoring(): void {
    this.isActive = false;

    if (this.processor) {
      this.processor.disconnect();
      this.processor = null;
    }

    if (this.audioContext) {
      this.audioContext.close();
      this.audioContext = null;
    }

    if (this.mediaStream) {
      this.mediaStream.getTracks().forEach(track => track.stop());
      this.mediaStream = null;
    }

    if (this.speechRecognition) {
      this.speechRecognition.stop();
    }

    console.log('Audio monitoring stopped');
  }

  private processAudioData(buffer: AudioBuffer): void {
    if (!this.isActive) return;

    // Convert audio buffer to Float32Array for processing
    const channelData = buffer.getChannelData(0);
    this.audioBuffer.push(new Float32Array(channelData));

    // Keep buffer size manageable (last 10 seconds)
    const maxBufferSize = Math.floor(44100 * 10 / 4096);
    if (this.audioBuffer.length > maxBufferSize) {
      this.audioBuffer.shift();
    }

    // Analyze audio for voice activity detection
    this.detectVoiceActivity(channelData);
  }

  private detectVoiceActivity(audioData: Float32Array): boolean {
    // Simple voice activity detection based on energy levels
    let energy = 0;
    for (let i = 0; i < audioData.length; i++) {
      energy += audioData[i] * audioData[i];
    }
    
    const averageEnergy = energy / audioData.length;
    const threshold = 0.01; // Adjust based on environment
    
    return averageEnergy > threshold;
  }

  private handleSpeechResult(event: any): void {
    let finalTranscript = '';
    let interimTranscript = '';

    for (let i = event.resultIndex; i < event.results.length; i++) {
      const transcript = event.results[i][0].transcript;
      
      if (event.results[i].isFinal) {
        finalTranscript += transcript;
      } else {
        interimTranscript += transcript;
      }
    }

    if (finalTranscript) {
      this.processTranscript(finalTranscript);
    }
  }

  private processTranscript(transcript: string): void {
    // Detect questions in the transcript
    const questions = this.detectQuestions(transcript);
    
    if (questions.length > 0) {
      // Emit event for detected questions
      this.emitQuestionDetected(questions);
    }
  }

  processAudioStream(stream: MediaStream): void {
    // Process external audio stream
    if (!this.audioContext) {
      this.audioContext = new AudioContext();
    }

    const source = this.audioContext.createMediaStreamSource(stream);
    const analyser = this.audioContext.createAnalyser();
    
    analyser.fftSize = 2048;
    source.connect(analyser);

    const dataArray = new Uint8Array(analyser.frequencyBinCount);
    
    const analyze = () => {
      if (!this.isActive) return;
      
      analyser.getByteFrequencyData(dataArray);
      // Process frequency data for advanced audio analysis
      
      requestAnimationFrame(analyze);
    };
    
    analyze();
  }

  async extractSpeechToText(audio: AudioBuffer): Promise<string> {
    // Convert AudioBuffer to text using Web Speech API or external service
    return new Promise((resolve, reject) => {
      if (!this.speechRecognition) {
        reject(new Error('Speech recognition not available'));
        return;
      }

      // Create a temporary audio context for processing
      const tempContext = new AudioContext();
      const source = tempContext.createBufferSource();
      source.buffer = audio;
      
      // Process audio buffer through speech recognition
      // This is a simplified implementation - in production, you'd use
      // a more robust speech-to-text service like OpenAI Whisper
      
      resolve('Processed speech text would appear here');
    });
  }

  detectQuestions(text: string): Question[] {
    const questions: Question[] = [];
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 0);

    sentences.forEach((sentence, index) => {
      const trimmed = sentence.trim();
      
      for (const pattern of this.questionPatterns) {
        const matches = trimmed.match(pattern);
        
        if (matches) {
          const question: Question = {
            id: `q_${Date.now()}_${index}`,
            text: trimmed,
            timestamp: Date.now(),
            confidence: this.calculateQuestionConfidence(trimmed, pattern),
            type: this.classifyQuestionType(trimmed),
            urgency: this.assessQuestionUrgency(trimmed),
            context: text
          };
          
          questions.push(question);
          break; // Only match first pattern to avoid duplicates
        }
      }
    });

    return questions;
  }

  private calculateQuestionConfidence(text: string, pattern: RegExp): number {
    // Calculate confidence based on pattern match strength and context
    let confidence = 0.5; // Base confidence
    
    // Boost confidence for explicit question words
    if (/\b(what|how|why|when|where|which)\b/i.test(text)) {
      confidence += 0.2;
    }
    
    // Boost confidence for question marks
    if (text.includes('?')) {
      confidence += 0.3;
    }
    
    // Reduce confidence for very short or very long questions
    if (text.length < 10 || text.length > 200) {
      confidence -= 0.1;
    }
    
    return Math.min(Math.max(confidence, 0), 1);
  }

  private classifyQuestionType(text: string): Question['type'] {
    const lowerText = text.toLowerCase();
    
    if (/\b(code|implement|algorithm|function|debug|programming|syntax)\b/.test(lowerText)) {
      return 'coding';
    }
    
    if (/\b(tell me about|describe|experience|situation|challenge|team|leadership)\b/.test(lowerText)) {
      return 'behavioral';
    }
    
    if (/\b(technical|system|architecture|database|api|performance|scalability)\b/.test(lowerText)) {
      return 'technical';
    }
    
    return 'general';
  }

  private assessQuestionUrgency(text: string): Question['urgency'] {
    const lowerText = text.toLowerCase();
    
    // Critical urgency indicators
    if (/\b(urgent|immediately|asap|critical|emergency)\b/.test(lowerText)) {
      return 'critical';
    }
    
    // High urgency indicators
    if (/\b(quickly|soon|important|priority|deadline)\b/.test(lowerText)) {
      return 'high';
    }
    
    // Medium urgency indicators
    if (/\b(when|how long|timeline|schedule)\b/.test(lowerText)) {
      return 'medium';
    }
    
    return 'low';
  }

  private emitQuestionDetected(questions: Question[]): void {
    // Emit custom event for question detection
    if (typeof window !== 'undefined') {
      const event = new CustomEvent('questionsDetected', {
        detail: { questions }
      });
      window.dispatchEvent(event);
    }
  }

  isMonitoring(): boolean {
    return this.isActive;
  }
}

  // Advanced audio analysis methods
  async analyzeAudioContext(audioData: Float32Array[]): Promise<AudioAnalysis> {
    // Combine all audio processing results
    const transcript = await this.processAudioBufferToText(audioData);
    const questions = this.detectQuestions(transcript);
    const speakers = await this.identifySpeakers(audioData);
    const sentiment = this.analyzeSentiment(transcript);
    const meetingPhase = this.detectMeetingPhase(transcript);

    return {
      transcript,
      questions,
      speakers,
      sentiment,
      meetingPhase
    };
  }

  private async processAudioBufferToText(audioData: Float32Array[]): Promise<string> {
    // Process accumulated audio buffer to text
    // This would integrate with a speech-to-text service
    return 'Processed transcript from audio buffer';
  }

  private async identifySpeakers(audioData: Float32Array[]): Promise<Speaker[]> {
    // Speaker identification and diarization
    // This would use voice recognition algorithms
    return [
      {
        id: 'speaker_1',
        name: 'Interviewer',
        confidence: 0.9,
        segments: []
      }
    ];
  }

  private analyzeSentiment(text: string): 'positive' | 'neutral' | 'negative' {
    // Simple sentiment analysis
    const positiveWords = ['good', 'great', 'excellent', 'perfect', 'amazing', 'wonderful'];
    const negativeWords = ['bad', 'terrible', 'awful', 'horrible', 'wrong', 'failed'];

    const lowerText = text.toLowerCase();
    const positiveCount = positiveWords.filter(word => lowerText.includes(word)).length;
    const negativeCount = negativeWords.filter(word => lowerText.includes(word)).length;

    if (positiveCount > negativeCount) return 'positive';
    if (negativeCount > positiveCount) return 'negative';
    return 'neutral';
  }

  private detectMeetingPhase(text: string): AudioAnalysis['meetingPhase'] {
    const lowerText = text.toLowerCase();

    if (/\b(hello|hi|introduction|nice to meet|tell me about yourself)\b/.test(lowerText)) {
      return 'introduction';
    }

    if (/\b(questions|any questions|do you have|final questions)\b/.test(lowerText)) {
      return 'questions';
    }

    if (/\b(thank you|thanks|that's all|goodbye|we'll be in touch)\b/.test(lowerText)) {
      return 'conclusion';
    }

    return 'discussion';
  }
}

// Export singleton instance
export const audioProcessor = new AdvancedAudioProcessor();

// Utility functions for audio processing
export const AudioUtils = {
  // Convert audio buffer to WAV format
  audioBufferToWav(buffer: AudioBuffer): ArrayBuffer {
    const length = buffer.length;
    const numberOfChannels = buffer.numberOfChannels;
    const sampleRate = buffer.sampleRate;
    const arrayBuffer = new ArrayBuffer(44 + length * numberOfChannels * 2);
    const view = new DataView(arrayBuffer);

    // WAV header
    const writeString = (offset: number, string: string) => {
      for (let i = 0; i < string.length; i++) {
        view.setUint8(offset + i, string.charCodeAt(i));
      }
    };

    writeString(0, 'RIFF');
    view.setUint32(4, 36 + length * numberOfChannels * 2, true);
    writeString(8, 'WAVE');
    writeString(12, 'fmt ');
    view.setUint32(16, 16, true);
    view.setUint16(20, 1, true);
    view.setUint16(22, numberOfChannels, true);
    view.setUint32(24, sampleRate, true);
    view.setUint32(28, sampleRate * numberOfChannels * 2, true);
    view.setUint16(32, numberOfChannels * 2, true);
    view.setUint16(34, 16, true);
    writeString(36, 'data');
    view.setUint32(40, length * numberOfChannels * 2, true);

    // Convert float samples to 16-bit PCM
    let offset = 44;
    for (let i = 0; i < length; i++) {
      for (let channel = 0; channel < numberOfChannels; channel++) {
        const sample = Math.max(-1, Math.min(1, buffer.getChannelData(channel)[i]));
        view.setInt16(offset, sample < 0 ? sample * 0x8000 : sample * 0x7FFF, true);
        offset += 2;
      }
    }

    return arrayBuffer;
  },

  // Calculate audio volume level
  calculateVolume(audioData: Float32Array): number {
    let sum = 0;
    for (let i = 0; i < audioData.length; i++) {
      sum += audioData[i] * audioData[i];
    }
    return Math.sqrt(sum / audioData.length);
  },

  // Apply noise reduction
  applyNoiseReduction(audioData: Float32Array, threshold: number = 0.01): Float32Array {
    const processed = new Float32Array(audioData.length);
    for (let i = 0; i < audioData.length; i++) {
      processed[i] = Math.abs(audioData[i]) > threshold ? audioData[i] : 0;
    }
    return processed;
  }
};
